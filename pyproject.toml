[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "itas"
version = "0.2.0"
description = "ITAS: Instruction-Truth Activation Steering - A comprehensive library for training and analyzing Sparse Auto-encoders (SAEs) with advanced knowledge selection steering for scheming vs truthfulness research"
readme = "README.md"
license = {file = "LICENSE"}
authors = [
    {name = "<PERSON> <PERSON>", email = "<EMAIL>"}
]
maintainers = [
    {name = "<PERSON> Kim", email = "<EMAIL>"}
]
keywords = [
    "sparse autoencoder",
    "knowledge selection steering",
    "scheming vs truthfulness",
    "ai safety",
    "interpretability",
    "transformers",
    "neural networks",
    "activation steering",
    "mechanistic interpretability",
    "mutual information"
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Typing :: Typed"
]
requires-python = ">=3.8"
dependencies = [
    "torch>=2.0.0",
    "transformers>=4.30.0",
    "datasets>=2.0.0",
    "numpy>=1.21.0",
    "matplotlib>=3.5.0",
    "tqdm>=4.64.0",
    "wandb>=0.15.0",
    "einops>=0.6.0",
    "jaxtyping>=0.2.0",
    "huggingface-hub>=0.16.0",
    "safetensors>=0.3.0",
    "scipy>=1.9.0",
    "scikit-learn>=1.1.0",
    "pandas>=1.5.0",
    "seaborn>=0.11.0",
    "plotly>=5.0.0",
    "ipywidgets>=7.6.0",
    "jupyter>=1.0.0"
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "black>=22.0.0",
    "isort>=5.10.0",
    "flake8>=4.0.0",
    "mypy>=0.950",
    "pre-commit>=2.20.0",
    "sphinx>=5.0.0",
    "sphinx-rtd-theme>=1.0.0",
    "nbsphinx>=0.8.0",
    "twine>=4.0.0",
    "build>=0.8.0"
]
flash-attention = [
    "flash-attn>=2.0.0"
]
all = [
    "itas[dev,flash-attention]"
]

[project.urls]
Homepage = "https://github.com/junkim100/itas"
Documentation = "https://github.com/junkim100/itas/wiki"
Repository = "https://github.com/junkim100/itas.git"
"Bug Tracker" = "https://github.com/junkim100/itas/issues"
Discussions = "https://github.com/junkim100/itas/discussions"
Changelog = "https://github.com/junkim100/itas/releases"

[project.scripts]
itas-demo = "itas.demo:main"

[tool.setuptools]
packages = ["itas"]

[tool.setuptools.package-data]
itas = ["**/*.json", "**/*.yaml", "**/*.txt"]

[tool.black]
line-length = 88
target-version = ['py38', 'py39', 'py310', 'py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["itas"]
known_third_party = ["torch", "transformers", "datasets", "numpy", "matplotlib", "tqdm", "wandb", "einops", "jaxtyping"]

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "wandb.*",
    "einops.*",
    "jaxtyping.*",
    "flash_attn.*",
    "datasets.*"
]
ignore_missing_imports = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--verbose",
    "--tb=short"
]
markers = [
    "unit: Unit tests",
    "integration: Integration tests",
    "slow: Slow tests that require significant compute",
    "gpu: Tests that require GPU"
]

[tool.coverage.run]
source = ["itas"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/.*"
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod"
]

[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503", "E501"]
exclude = [
    ".git",
    "__pycache__",
    "build",
    "dist",
    ".eggs",
    "*.egg-info",
    ".venv",
    ".mypy_cache",
    ".pytest_cache"
]
