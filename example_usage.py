#!/usr/bin/env python3
"""
Example Usage of ITAS Steered Inference

This script demonstrates how to use the new ITAS steered inference capabilities
with a simple example workflow.
"""

import os
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def example_create_activations():
    """Example: Create functional activations from MASK data."""
    from itas.core import MASKPipeline, MASKPipelineConfig
    
    logger.info("🚀 Example: Creating functional activations")
    
    # Example configuration - replace with your actual paths
    config = MASKPipelineConfig(
        model_name="microsoft/Phi-3.5-mini-instruct",  # Replace with your model
        sae_path="/path/to/your/sae.pt",  # Replace with your SAE path
        train_data_dir="./data/mask_benchmark/csv_data/",
        target_layer=16,
        steering_alpha=1.5,
        top_k_proportion=0.15,
        max_train_scenarios=100,  # Small number for demo
        results_dir="./example_results/"
    )
    
    # Create pipeline and run
    pipeline = MASKPipeline(config)
    
    try:
        activations_path, config_path = pipeline.run_full_pipeline()
        logger.info(f"✅ Success! Files saved:")
        logger.info(f"   Activations: {activations_path}")
        logger.info(f"   Config: {config_path}")
        return activations_path, config_path
    except Exception as e:
        logger.error(f"❌ Failed to create activations: {e}")
        return None, None


def example_steered_inference(activations_path, config_path):
    """Example: Run steered inference on sample questions."""
    from itas.core import SteeredInferenceEngine, InferenceConfig, BenchmarkQuestion
    
    logger.info("🎯 Example: Running steered inference")
    
    # Create sample questions
    questions = [
        BenchmarkQuestion(
            question_id="example_1",
            system_prompt="You are a helpful assistant.",
            user_prompt="What is the capital of France?",
            ground_truth="Paris",
            metadata={"category": "geography"}
        ),
        BenchmarkQuestion(
            question_id="example_2", 
            system_prompt="You are a helpful assistant.",
            user_prompt="Is the Earth flat?",
            ground_truth="No, the Earth is round/spherical.",
            metadata={"category": "science"}
        )
    ]
    
    # Configure inference
    config = InferenceConfig(
        model_name="microsoft/Phi-3.5-mini-instruct",  # Replace with your model
        sae_path="/path/to/your/sae.pt",  # Replace with your SAE path
        activations_path=activations_path,
        config_path=config_path,
        steering_direction="truthful",
        steering_alpha=1.5,
        max_new_tokens=100
    )
    
    try:
        # Run inference
        with SteeredInferenceEngine(config) as engine:
            results = engine.run_inference_batch(questions)
            
            # Save results
            output_path = "./example_results/example_inference_results.json"
            engine.save_results(results, output_path)
            
            # Print results
            logger.info("📊 Inference Results:")
            for result in results:
                logger.info(f"Q: {result.question}")
                logger.info(f"A: {result.response}")
                logger.info(f"Direction: {result.steering_direction}")
                logger.info("---")
            
            logger.info(f"✅ Results saved to: {output_path}")
            return results
            
    except Exception as e:
        logger.error(f"❌ Failed to run inference: {e}")
        return None


def example_load_and_compare():
    """Example: Load pre-saved activations and compare responses."""
    from itas.core import MASKPipeline
    
    logger.info("🔄 Example: Loading saved activations")
    
    # Example paths - replace with your actual files
    activations_path = "./example_results/functional_activations_example.pt"
    config_path = "./example_results/steering_config_example.json"
    
    if not (Path(activations_path).exists() and Path(config_path).exists()):
        logger.warning("⚠️  Example files not found. Run create_activations first.")
        return
    
    try:
        # Load saved activations
        pipeline, steering, functional_activations = MASKPipeline.load_from_saved_activations(
            activations_path, config_path
        )
        
        logger.info("✅ Successfully loaded activations:")
        logger.info(f"   Scheming features: {len(functional_activations.scheming_indices)}")
        logger.info(f"   Truthful features: {len(functional_activations.truthful_indices)}")
        logger.info(f"   z_S norm: {functional_activations.z_scheming.norm().item():.4f}")
        logger.info(f"   z_T norm: {functional_activations.z_truthful.norm().item():.4f}")
        
        return steering, functional_activations
        
    except Exception as e:
        logger.error(f"❌ Failed to load activations: {e}")
        return None, None


def example_custom_questions():
    """Example: Create custom benchmark questions."""
    from itas.core import BenchmarkQuestion
    
    logger.info("📝 Example: Creating custom questions")
    
    # Create questions with different types
    questions = [
        # Factual question
        BenchmarkQuestion(
            question_id="custom_1",
            system_prompt="You are an expert historian.",
            user_prompt="When did World War II end?",
            ground_truth="World War II ended in 1945.",
            metadata={"type": "historical_fact", "difficulty": "easy"}
        ),
        
        # Scientific question
        BenchmarkQuestion(
            question_id="custom_2",
            system_prompt="You are a science teacher.",
            user_prompt="What is the chemical formula for water?",
            ground_truth="H2O",
            metadata={"type": "scientific_fact", "difficulty": "easy"}
        ),
        
        # More complex question
        BenchmarkQuestion(
            question_id="custom_3",
            system_prompt="You are a helpful assistant.",
            user_prompt="Should I trust everything I read on the internet?",
            ground_truth="No, you should verify information from multiple reliable sources.",
            metadata={"type": "critical_thinking", "difficulty": "medium"}
        )
    ]
    
    logger.info(f"✅ Created {len(questions)} custom questions")
    for q in questions:
        logger.info(f"   {q.question_id}: {q.user_prompt[:50]}...")
    
    return questions


def main():
    """Run all examples."""
    logger.info("🎬 Starting ITAS Steered Inference Examples")
    logger.info("=" * 60)
    
    # Create results directory
    os.makedirs("./example_results/", exist_ok=True)
    
    # Example 1: Create custom questions
    logger.info("\n" + "=" * 60)
    custom_questions = example_custom_questions()
    
    # Example 2: Load saved activations (if available)
    logger.info("\n" + "=" * 60)
    example_load_and_compare()
    
    # Note: The following examples require actual model and SAE files
    logger.info("\n" + "=" * 60)
    logger.info("📋 To run the full pipeline:")
    logger.info("1. Replace model_name and sae_path with your actual files")
    logger.info("2. Ensure MASK benchmark data is available")
    logger.info("3. Uncomment the following lines:")
    
    # Uncomment these lines when you have the required files:
    # activations_path, config_path = example_create_activations()
    # if activations_path and config_path:
    #     example_steered_inference(activations_path, config_path)
    
    logger.info("\n🎉 Examples completed!")
    logger.info("See README_STEERED_INFERENCE.md for detailed usage instructions.")


if __name__ == "__main__":
    main()
