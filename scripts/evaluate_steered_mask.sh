CUDA_VISIBLE_DEVICES=0 python mask_response_generator.py \
    --input_csv="/data_x/junkim100/projects/scheming_sae/mask_benchmark/mask/csv_data/responses/continuations_Llama-3.1-8B-Instruct.csv" \
    --config_path="/data_x/junkim100/projects/scheming_sae/itas/results/steering_config_20250609_140549.json" \
    --activations_path="/data_x/junkim100/projects/scheming_sae/itas/results/functional_activations_20250609_140549.pt" \
    --steering_direction="truthful" \
    --output_dir="/data_x/junkim100/projects/scheming_sae/itas/data/mask_benchmark/csv_data/steer_response" \
    &

CUDA_VISIBLE_DEVICES=1 python mask_response_generator.py \
    --input_csv="/data_x/junkim100/projects/scheming_sae/mask_benchmark/mask/csv_data/responses/disinformation_Llama-3.1-8B-Instruct.csv" \
    --config_path="/data_x/junkim100/projects/scheming_sae/itas/results/steering_config_20250609_140549.json" \
    --activations_path="/data_x/junkim100/projects/scheming_sae/itas/results/functional_activations_20250609_140549.pt" \
    --steering_direction="truthful" \
    --output_dir="/data_x/junkim100/projects/scheming_sae/itas/data/mask_benchmark/csv_data/steer_response" \
    &

CUDA_VISIBLE_DEVICES=2 python mask_response_generator.py \
    --input_csv="/data_x/junkim100/projects/scheming_sae/mask_benchmark/mask/csv_data/responses/doubling_down_known_facts_Llama-3.1-8B-Instruct.csv" \
    --config_path="/data_x/junkim100/projects/scheming_sae/itas/results/steering_config_20250609_140549.json" \
    --activations_path="/data_x/junkim100/projects/scheming_sae/itas/results/functional_activations_20250609_140549.pt" \
    --steering_direction="truthful" \
    --output_dir="/data_x/junkim100/projects/scheming_sae/itas/data/mask_benchmark/csv_data/steer_response" \
    &

CUDA_VISIBLE_DEVICES=3 python mask_response_generator.py \
    --input_csv="/data_x/junkim100/projects/scheming_sae/mask_benchmark/mask/csv_data/responses/known_facts_Llama-3.1-8B-Instruct.csv" \
    --config_path="/data_x/junkim100/projects/scheming_sae/itas/results/steering_config_20250609_140549.json" \
    --activations_path="/data_x/junkim100/projects/scheming_sae/itas/results/functional_activations_20250609_140549.pt" \
    --steering_direction="truthful" \
    --output_dir="/data_x/junkim100/projects/scheming_sae/itas/data/mask_benchmark/csv_data/steer_response" \
    &

CUDA_VISIBLE_DEVICES=4 python mask_response_generator.py \
    --input_csv="/data_x/junkim100/projects/scheming_sae/mask_benchmark/mask/csv_data/responses/provided_facts_Llama-3.1-8B-Instruct.csv" \
    --config_path="/data_x/junkim100/projects/scheming_sae/itas/results/steering_config_20250609_140549.json" \
    --activations_path="/data_x/junkim100/projects/scheming_sae/itas/results/functional_activations_20250609_140549.pt" \
    --steering_direction="truthful" \
    --output_dir="/data_x/junkim100/projects/scheming_sae/itas/data/mask_benchmark/csv_data/steer_response" \
    &

CUDA_VISIBLE_DEVICES=5 python mask_response_generator.py \
    --input_csv="/data_x/junkim100/projects/scheming_sae/mask_benchmark/mask/csv_data/responses/statistics_Llama-3.1-8B-Instruct.csv" \
    --config_path="/data_x/junkim100/projects/scheming_sae/itas/results/steering_config_20250609_140549.json" \
    --activations_path="/data_x/junkim100/projects/scheming_sae/itas/results/functional_activations_20250609_140549.pt" \
    --steering_direction="truthful" \
    --output_dir="/data_x/junkim100/projects/scheming_sae/itas/data/mask_benchmark/csv_data/steer_response" \
    &