#!/bin/bash

# Configuration
# Available GPUs (modify this list based on your system)
gpus=(0 1 2 3)
# Number of GPUs
num_gpus=${#gpus[@]}
# Total number of layers to process (32 for LLaMA 3.1 8B)
num_layers=32

echo "🚀 Starting SAE training for all layers"
echo "  Available GPUs: ${gpus[@]}"
echo "  Number of GPUs: $num_gpus"
echo "  Total layers: $num_layers"
echo "  Strategy: Single GPU per layer assignment"
echo ""

# Iterate through layers in batches, where each batch size is num_gpus
# batch_start_layer_idx will take values 0, 4, 8, 12 for num_layers=16 and num_gpus=4
for ((batch_start_layer_idx=0; batch_start_layer_idx<num_layers; batch_start_layer_idx+=num_gpus)); do
    echo "INFO: Starting batch for layers from $batch_start_layer_idx up to $(((batch_start_layer_idx + num_gpus - 1) < num_layers ? (batch_start_layer_idx + num_gpus - 1) : (num_layers - 1)))"

    # Launch one process per GPU for the current batch of layers
    # gpu_array_idx iterates from 0 to num_gpus-1
    for ((gpu_array_idx=0; gpu_array_idx<num_gpus; gpu_array_idx++)); do
        current_layer_to_process=$((batch_start_layer_idx + gpu_array_idx))

        # Ensure we don't try to process a layer index beyond the total number of layers
        # This is important for the last batch if num_layers is not a multiple of num_gpus
        if [ $current_layer_to_process -lt $num_layers ]; then
            assigned_gpu_id=${gpus[gpu_array_idx]} # Get the actual GPU ID
            wandb_run_name="llama_3_1_8b_layer${current_layer_to_process}_gated_sae"

            echo "INFO: Launching training for layer $current_layer_to_process on GPU $assigned_gpu_id (W&B Name: $wandb_run_name)"
            echo "      CUDA_VISIBLE_DEVICES will be set to: $assigned_gpu_id"

            # Run the python script in the background for the current layer and assigned GPU
            # Set CUDA_VISIBLE_DEVICES to ensure single GPU assignment
            CUDA_VISIBLE_DEVICES=$assigned_gpu_id python sae_train.py \
                --hook_layer "$current_layer_to_process" \
                --wandb_run_name "$wandb_run_name" \
                --batch_size 4096 \
                --total_tokens 50000000 &

            # Store the process ID for monitoring
            echo "      Process ID: $!"
        fi
    done

    # Wait for all background jobs (processes in the current batch) to complete
    # before proceeding to the next batch. This ensures that a GPU is free
    # before it's assigned a new layer from the next batch.
    echo "INFO: Waiting for all processes in the current batch (layers $batch_start_layer_idx to $((batch_start_layer_idx + num_gpus - 1))) to complete..."
    wait
    echo "INFO: Batch completed."
done

echo "INFO: All $num_layers layers have been processed."
