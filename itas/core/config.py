"""
Configuration classes for SpARE SAE training and inference.

This module provides comprehensive configuration management for all aspects
of SAE training, model loading, and dataset handling.
"""

from dataclasses import dataclass, field
from typing import Optional, Dict, Any, List, Union, Literal
from pathlib import Path
import torch
import json


@dataclass
class ModelConfig:
    """Configuration for model loading and setup."""

    # Model identification
    model_name: str = "microsoft/DialoGPT-medium"
    """HuggingFace model name or path to local model"""

    model_revision: Optional[str] = None
    """Specific model revision/branch to use"""

    # Model loading parameters
    torch_dtype: Union[str, torch.dtype] = "auto"
    """Data type for model weights (auto, float16, bfloat16, float32)"""

    device_map: Union[str, Dict[str, Any]] = "auto"
    """Device mapping strategy for multi-GPU setups"""

    trust_remote_code: bool = False
    """Whether to trust remote code in model repositories"""

    use_flash_attention: bool = True
    """Whether to use Flash Attention 2 if available"""

    load_in_8bit: bool = False
    """Whether to load model in 8-bit precision"""

    load_in_4bit: bool = False
    """Whether to load model in 4-bit precision"""

    # Model type selection
    load_for_generation: bool = False
    """Whether to load model for text generation (uses AutoModelForCausalLM)"""

    # Model-specific parameters
    max_position_embeddings: Optional[int] = None
    """Override model's max position embeddings"""

    model_kwargs: Dict[str, Any] = field(default_factory=dict)
    """Additional keyword arguments for model loading"""

    def __post_init__(self):
        """Validate configuration after initialization."""
        if isinstance(self.torch_dtype, str):
            dtype_map = {
                "auto": "auto",
                "float16": torch.float16,
                "bfloat16": torch.bfloat16,
                "float32": torch.float32,
            }
            if self.torch_dtype in dtype_map:
                self.torch_dtype = dtype_map[self.torch_dtype]
            else:
                raise ValueError(f"Invalid torch_dtype: {self.torch_dtype}")


@dataclass
class DatasetConfig:
    """Configuration for dataset loading and processing."""

    # Dataset identification
    dataset_name: str = "togethercomputer/RedPajama-Data-1T-Sample"
    """HuggingFace dataset name or path to local dataset"""

    dataset_split: str = "train"
    """Dataset split to use (train, validation, test)"""

    dataset_revision: Optional[str] = None
    """Specific dataset revision to use"""

    # Dataset processing
    text_column: str = "text"
    """Column name containing the text data"""

    max_seq_length: int = 2048
    """Maximum sequence length for tokenization"""

    streaming: bool = False
    """Whether to use streaming dataset loading"""

    trust_remote_code: bool = False
    """Whether to trust remote code in dataset repositories"""

    # Preprocessing parameters
    chunk_size: int = 2048
    """Size of text chunks for processing"""

    overlap_size: int = 0
    """Overlap between consecutive chunks"""

    num_proc: Optional[int] = None
    """Number of processes for dataset processing"""

    # Caching
    cache_dir: Optional[str] = None
    """Directory for caching processed datasets"""

    load_from_cache_file: bool = True
    """Whether to load from cached files if available"""

    dataset_kwargs: Dict[str, Any] = field(default_factory=dict)
    """Additional keyword arguments for dataset loading"""


@dataclass
class TrainingConfig:
    """Configuration for SAE training process."""

    # Training parameters
    total_training_tokens: int = 2_000_000
    """Total number of tokens to train on"""

    batch_size: int = 4096
    """Training batch size in tokens"""

    learning_rate: float = 3e-4
    """Learning rate for optimizer"""

    lr_scheduler: str = "constant"
    """Learning rate scheduler type"""

    lr_warm_up_steps: int = 0
    """Number of warmup steps for learning rate"""

    lr_decay_steps: Optional[int] = None
    """Number of decay steps for learning rate"""

    # Regularization
    l1_coefficient: float = 1e-3
    """L1 regularization coefficient for sparsity"""

    l1_warm_up_steps: int = 0
    """Number of steps to warm up L1 coefficient"""

    # Optimizer parameters
    adam_beta1: float = 0.9
    """Adam optimizer beta1 parameter"""

    adam_beta2: float = 0.999
    """Adam optimizer beta2 parameter"""

    weight_decay: float = 0.0
    """Weight decay for optimizer"""

    # Training dynamics
    dead_feature_threshold: float = 1e-8
    """Threshold for considering features as dead"""

    feature_sampling_window: int = 1000
    """Window size for feature sampling statistics"""

    # Checkpointing
    checkpoint_every_n_tokens: int = 100_000
    """Save checkpoint every N tokens"""

    save_checkpoint_dir: Optional[str] = None
    """Directory to save checkpoints"""

    # Logging and monitoring
    log_every_n_steps: int = 100
    """Log training metrics every N steps"""

    eval_every_n_tokens: int = 100_000
    """Run evaluation every N tokens"""

    use_wandb: bool = False
    """Whether to use Weights & Biases for logging"""

    wandb_project: Optional[str] = None
    """W&B project name"""

    wandb_entity: Optional[str] = None
    """W&B entity name"""


@dataclass
class SAEConfig:
    """Main configuration class for Sparse Auto-Encoder."""

    # Model configuration
    model: ModelConfig = field(default_factory=ModelConfig)
    """Model configuration"""

    # Dataset configuration
    dataset: DatasetConfig = field(default_factory=DatasetConfig)
    """Dataset configuration"""

    # Training configuration
    training: TrainingConfig = field(default_factory=TrainingConfig)
    """Training configuration"""

    # SAE Architecture
    architecture: Literal["standard", "gated", "jumprelu"] = "standard"
    """SAE architecture type"""

    d_in: Optional[int] = None
    """Input dimension (auto-detected if None)"""

    expansion_factor: int = 32
    """Expansion factor for SAE hidden dimension"""

    d_sae: Optional[int] = None
    """SAE hidden dimension (computed from expansion_factor if None)"""

    # Activation function
    activation_fn: str = "relu"
    """Activation function for SAE"""

    normalize_decoder: bool = True
    """Whether to normalize decoder weights"""

    # Hook configuration
    hook_layer: int = 12
    """Layer to hook for activation extraction"""

    hook_name: str = "blocks.{layer}.hook_resid_post"
    """Hook name pattern (use {layer} for layer substitution)"""

    hook_head_index: Optional[int] = None
    """Head index for attention hooks (None for MLP/residual)"""

    # Preprocessing
    prepend_bos: bool = True
    """Whether to prepend BOS token"""

    normalize_activations: str = "none"
    """Activation normalization method (none, layer_norm, rms_norm)"""

    # Device and precision
    device: str = "cuda" if torch.cuda.is_available() else "cpu"
    """Device for training and inference"""

    dtype: str = "float32"
    """Data type for SAE parameters"""

    # Miscellaneous
    seed: int = 42
    """Random seed for reproducibility"""

    def __post_init__(self):
        """Validate and compute derived parameters."""
        # Compute d_sae if not provided
        if self.d_sae is None and self.d_in is not None:
            self.d_sae = self.d_in * self.expansion_factor

        # Format hook name with layer
        if "{layer}" in self.hook_name:
            self.hook_name = self.hook_name.format(layer=self.hook_layer)

        # Validate architecture
        valid_architectures = ["standard", "gated", "jumprelu"]
        if self.architecture not in valid_architectures:
            raise ValueError(f"Invalid architecture: {self.architecture}")

    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> "SAEConfig":
        """Create config from dictionary."""
        # Handle nested configurations
        model_config = config_dict.pop("model", {})
        dataset_config = config_dict.pop("dataset", {})
        training_config = config_dict.pop("training", {})

        return cls(
            model=ModelConfig(**model_config),
            dataset=DatasetConfig(**dataset_config),
            training=TrainingConfig(**training_config),
            **config_dict,
        )

    @classmethod
    def from_json(cls, json_path: Union[str, Path]) -> "SAEConfig":
        """Load configuration from JSON file."""
        with open(json_path, "r") as f:
            config_dict = json.load(f)
        return cls.from_dict(config_dict)

    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary."""
        config_dict = {}

        # Convert dataclass fields to dict
        for field_name, field_value in self.__dict__.items():
            if isinstance(field_value, (ModelConfig, DatasetConfig, TrainingConfig)):
                config_dict[field_name] = field_value.__dict__
            else:
                config_dict[field_name] = field_value

        return config_dict

    def save_json(self, json_path: Union[str, Path]) -> None:
        """Save configuration to JSON file."""
        with open(json_path, "w") as f:
            json.dump(self.to_dict(), f, indent=2, default=str)


@dataclass
class ClassifierTrainingConfig:
    """Configuration for classifier training."""

    learning_rate: float = 0.002
    num_epochs: int = 100
    batch_size: int = 32
    l1_coefficient: float = 1e-3
    early_stopping_patience: int = 10
    validation_split: float = 0.2
    random_state: int = 42
    device: str = "cuda" if torch.cuda.is_available() else "cpu"
