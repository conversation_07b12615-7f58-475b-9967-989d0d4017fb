"""
Core components for ITAS (Instruction-Truth Activation Steering)

This module contains the main classes and functions for SAE training and inference,
designed to work with any HuggingFace transformer model.
"""

from .config import (
    SAEConfig,
    TrainingConfig,
    ModelConfig,
    DatasetConfig,
    ClassifierTrainingConfig,
)
from .sae import SAE, TrainingSAE
from .trainer import SAETrainer
from .model_loader import UniversalModelLoader
from .dataset_manager import DatasetManager
from .activations_store import ActivationsStore
from .sae_activation_classifier import SAEActivationClassifier
from .steering import (
    KnowledgeSelectionSteering,
    MutualInformationResult,
    FunctionalActivations,
    KnowledgeSteeringResult,
)
from .mask_pipeline import (
    MASKPipeline,
    MASKPipelineConfig,
    MASKScenario,
    MASKDataProcessor,
)
from .steered_inference import (
    SteeredInferenceEngine,
    InferenceConfig,
    BenchmarkQuestion,
    InferenceResult,
)

__all__ = [
    # Configuration
    "SAEConfig",
    "TrainingConfig",
    "ModelConfig",
    "DatasetConfig",
    "ClassifierTrainingConfig",
    # Core SAE classes
    "SAE",
    "TrainingSAE",
    "SAETrainer",
    # Utilities
    "UniversalModelLoader",
    "DatasetManager",
    "ActivationsStore",
    # Classifier
    "SAEActivationClassifier",
    # Steering
    "KnowledgeSelectionSteering",
    "MutualInformationResult",
    "FunctionalActivations",
    "KnowledgeSteeringResult",
    # MASK Pipeline
    "MASKPipeline",
    "MASKPipelineConfig",
    "MASKScenario",
    "MASKDataProcessor",
    # Steered Inference
    "SteeredInferenceEngine",
    "InferenceConfig",
    "BenchmarkQuestion",
    "InferenceResult",
]
