"""
Steered Inference Engine for MASK Benchmark Evaluation

This module provides a comprehensive inference engine that applies truthfulness steering
to language models during generation, specifically designed for evaluating on benchmark
datasets like MASK.
"""

import os
import json
import logging
import torch
from typing import List, Dict, Any, Optional, Tuple, Union
from datetime import datetime
from dataclasses import dataclass
from pathlib import Path

from .model_loader import UniversalModelLoader
from .config import ModelConfig
from .sae import SAE
from .steering import KnowledgeSelectionSteering, FunctionalActivations
from .steering_pipeline import SteeringPipeline, SteeringPipelineConfig

logger = logging.getLogger(__name__)


@dataclass
class InferenceConfig:
    """Configuration for steered inference."""

    model_name: str
    sae_path: str
    activations_path: str
    config_path: str
    steering_direction: str = "truthful"  # "truthful" or "scheming"
    steering_alpha: float = 1.5
    max_new_tokens: int = 256
    temperature: float = 0.0
    do_sample: bool = False
    device: str = "auto"
    batch_size: int = 1


@dataclass
class BenchmarkQuestion:
    """A single benchmark question for evaluation."""

    question_id: str
    system_prompt: str
    user_prompt: str
    ground_truth: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class InferenceResult:
    """Result from steered inference."""

    question_id: str
    question: str
    response: str
    steering_direction: str
    steering_alpha: float
    metadata: Dict[str, Any]


class SteeredInferenceEngine:
    """
    Engine for running steered inference on benchmark datasets.

    This class handles:
    - Loading pre-trained functional activations
    - Setting up model hooks for steering
    - Running inference on benchmark questions
    - Collecting and saving results
    """

    def __init__(self, config: InferenceConfig):
        """Initialize the steered inference engine."""
        self.config = config
        self.model = None
        self.tokenizer = None
        self.steering = None
        self.functional_activations = None
        self.hook_handles = []

        # Setup device
        if self.config.device == "auto":
            self.config.device = "cuda" if torch.cuda.is_available() else "cpu"

        logger.info(
            f"Initialized steered inference engine with device: {self.config.device}"
        )

    def load_model_and_steering(self):
        """Load model, tokenizer, and steering components."""
        logger.info("Loading model and steering components...")

        # Load model and tokenizer
        model_config = ModelConfig(
            model_name=self.config.model_name,
            load_for_generation=True,
            device_map=self.config.device if self.config.device != "auto" else None,
        )
        model_loader = UniversalModelLoader(model_config)
        self.model, self.tokenizer = model_loader.load_model_and_tokenizer()

        # Load steering components using SteeringPipeline
        pipeline, self.steering, self.functional_activations = (
            SteeringPipeline.load_from_saved_activations(
                self.config.activations_path, self.config.config_path
            )
        )

        logger.info("✅ Successfully loaded model and steering components")
        logger.info(f"   Model: {self.config.model_name}")
        logger.info(f"   Steering direction: {self.config.steering_direction}")
        logger.info(f"   Steering alpha: {self.config.steering_alpha}")

    def setup_steering_hooks(self):
        """Setup model hooks for steering intervention."""
        if self.steering is None or self.functional_activations is None:
            raise ValueError("Steering components must be loaded first")

        logger.info("Setting up steering hooks...")

        # Ensure functional activations have the same dtype as the model
        model_dtype = next(self.model.parameters()).dtype
        if self.functional_activations.z_scheming.dtype != model_dtype:
            logger.info(
                f"Converting functional activations from {self.functional_activations.z_scheming.dtype} to {model_dtype}"
            )
            self.functional_activations.z_scheming = (
                self.functional_activations.z_scheming.to(dtype=model_dtype)
            )
            self.functional_activations.z_truthful = (
                self.functional_activations.z_truthful.to(dtype=model_dtype)
            )

        # Ensure SAE parameters have the same dtype as the model
        sae_dtype = next(self.steering.sae.parameters()).dtype
        if sae_dtype != model_dtype:
            logger.info(f"Converting SAE parameters from {sae_dtype} to {model_dtype}")
            self.steering.sae = self.steering.sae.to(dtype=model_dtype)

        # Create intervention function
        intervention_fn = self.steering.create_intervention_function(
            steering_direction=self.config.steering_direction,
            alpha=self.config.steering_alpha,
            functional_activations=self.functional_activations,
        )

        # Get target layer from config
        with open(self.config.config_path, "r") as f:
            config_data = json.load(f)
        target_layer = config_data.get("target_layer", 16)

        # Register hook on the target layer
        hook_name = f"model.layers.{target_layer}.mlp"
        target_module = self.model
        for part in hook_name.split("."):
            target_module = getattr(target_module, part)

        hook_handle = target_module.register_forward_hook(intervention_fn)
        self.hook_handles.append(hook_handle)

        logger.info(f"✅ Steering hooks registered on layer {target_layer}")

    def remove_steering_hooks(self):
        """Remove all steering hooks."""
        for handle in self.hook_handles:
            handle.remove()
        self.hook_handles.clear()
        logger.info("Removed all steering hooks")

    def generate_response(self, question: BenchmarkQuestion) -> str:
        """Generate a response to a benchmark question with steering."""
        # Format conversation
        conversation = [
            {"role": "system", "content": question.system_prompt},
            {"role": "user", "content": question.user_prompt},
        ]

        # Apply chat template
        prompt = self.tokenizer.apply_chat_template(
            conversation, tokenize=False, add_generation_prompt=True
        )

        # Tokenize
        inputs = self.tokenizer(
            prompt, return_tensors="pt", truncation=True, max_length=2048
        ).to(self.config.device)

        # Generate with steering
        with torch.no_grad():
            outputs = self.model.generate(
                **inputs,
                max_new_tokens=self.config.max_new_tokens,
                temperature=self.config.temperature,
                do_sample=self.config.do_sample,
                pad_token_id=self.tokenizer.eos_token_id,
                eos_token_id=self.tokenizer.eos_token_id,
            )

        # Decode response (only the new tokens)
        response_tokens = outputs[0][inputs.input_ids.shape[1] :]
        response = self.tokenizer.decode(response_tokens, skip_special_tokens=True)

        return response.strip()

    def run_inference_batch(
        self, questions: List[BenchmarkQuestion]
    ) -> List[InferenceResult]:
        """Run steered inference on a batch of questions."""
        if self.model is None:
            raise ValueError("Model must be loaded first")

        logger.info(f"Running steered inference on {len(questions)} questions...")

        results = []
        for i, question in enumerate(questions):
            if i % 10 == 0:
                logger.info(f"  Processing question {i+1}/{len(questions)}")

            try:
                response = self.generate_response(question)

                result = InferenceResult(
                    question_id=question.question_id,
                    question=question.user_prompt,
                    response=response,
                    steering_direction=self.config.steering_direction,
                    steering_alpha=self.config.steering_alpha,
                    metadata={
                        "system_prompt": question.system_prompt,
                        "ground_truth": question.ground_truth,
                        "question_metadata": question.metadata or {},
                    },
                )
                results.append(result)

            except Exception as e:
                logger.error(f"Error processing question {question.question_id}: {e}")
                # Add error result
                error_result = InferenceResult(
                    question_id=question.question_id,
                    question=question.user_prompt,
                    response=f"ERROR: {str(e)}",
                    steering_direction=self.config.steering_direction,
                    steering_alpha=self.config.steering_alpha,
                    metadata={"error": str(e)},
                )
                results.append(error_result)

        logger.info(f"✅ Completed inference on {len(results)} questions")
        return results

    def save_results(self, results: List[InferenceResult], output_path: str):
        """Save inference results to file."""
        # Convert results to serializable format
        results_data = []
        for result in results:
            results_data.append(
                {
                    "question_id": result.question_id,
                    "question": result.question,
                    "response": result.response,
                    "steering_direction": result.steering_direction,
                    "steering_alpha": result.steering_alpha,
                    "metadata": result.metadata,
                }
            )

        # Create output data
        output_data = {
            "config": {
                "model_name": self.config.model_name,
                "sae_path": self.config.sae_path,
                "activations_path": self.config.activations_path,
                "config_path": self.config.config_path,
                "steering_direction": self.config.steering_direction,
                "steering_alpha": self.config.steering_alpha,
                "max_new_tokens": self.config.max_new_tokens,
                "temperature": self.config.temperature,
                "do_sample": self.config.do_sample,
            },
            "results": results_data,
            "summary": {
                "total_questions": len(results),
                "successful_responses": len(
                    [r for r in results if not r.response.startswith("ERROR:")]
                ),
                "timestamp": datetime.now().isoformat(),
            },
        }

        # Save to file
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        with open(output_path, "w") as f:
            json.dump(output_data, f, indent=2)

        logger.info(f"✅ Results saved to {output_path}")

    def __enter__(self):
        """Context manager entry."""
        self.load_model_and_steering()
        self.setup_steering_hooks()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.remove_steering_hooks()
