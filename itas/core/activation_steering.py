"""
Activation Steering for Direct Model Intervention

This module provides activation steering capabilities that directly modify
model activations during forward passes, without requiring SAE decomposition.
This is useful for simpler steering scenarios and faster inference.
"""

import torch
import torch.nn as nn
import logging
from typing import Dict, List, Optional, Callable, Tuple, Any
from dataclasses import dataclass
from pathlib import Path
import json

logger = logging.getLogger(__name__)


@dataclass
class ActivationSteeringConfig:
    """Configuration for activation steering."""
    
    target_layers: List[int]
    steering_vectors: Dict[int, torch.Tensor]  # layer -> steering vector
    steering_strength: float = 1.0
    steering_direction: str = "positive"  # "positive" or "negative"
    intervention_type: str = "add"  # "add", "replace", "scale"
    normalize_vectors: bool = True


class ActivationSteeringHook:
    """Hook for applying activation steering during forward pass."""
    
    def __init__(
        self,
        layer_idx: int,
        steering_vector: torch.Tensor,
        strength: float = 1.0,
        direction: str = "positive",
        intervention_type: str = "add"
    ):
        self.layer_idx = layer_idx
        self.steering_vector = steering_vector.detach()
        self.strength = strength
        self.direction = direction
        self.intervention_type = intervention_type
        
        # Normalize steering vector if needed
        if self.steering_vector.norm() > 0:
            self.steering_vector = self.steering_vector / self.steering_vector.norm()
    
    def __call__(self, module: nn.Module, input: torch.Tensor, output: torch.Tensor) -> torch.Tensor:
        """Apply steering intervention to the output."""
        if isinstance(output, tuple):
            hidden_states = output[0]
            rest = output[1:]
        else:
            hidden_states = output
            rest = ()
        
        # Apply steering
        steering_effect = self.steering_vector.to(hidden_states.device)
        
        # Apply direction
        if self.direction == "negative":
            steering_effect = -steering_effect
        
        # Apply strength
        steering_effect = steering_effect * self.strength
        
        # Apply intervention type
        if self.intervention_type == "add":
            # Add steering vector to all positions
            if len(hidden_states.shape) == 3:  # [batch, seq, hidden]
                steering_effect = steering_effect.unsqueeze(0).unsqueeze(0)
                hidden_states = hidden_states + steering_effect
            elif len(hidden_states.shape) == 2:  # [batch, hidden]
                steering_effect = steering_effect.unsqueeze(0)
                hidden_states = hidden_states + steering_effect
        
        elif self.intervention_type == "replace":
            # Replace last token with steering vector
            if len(hidden_states.shape) == 3:
                hidden_states[:, -1, :] = steering_effect.unsqueeze(0)
            elif len(hidden_states.shape) == 2:
                hidden_states = steering_effect.unsqueeze(0).expand_as(hidden_states)
        
        elif self.intervention_type == "scale":
            # Scale activations by steering vector
            hidden_states = hidden_states * (1 + steering_effect.unsqueeze(0).unsqueeze(0))
        
        # Return in original format
        if rest:
            return (hidden_states,) + rest
        else:
            return hidden_states


class ActivationSteeringEngine:
    """Engine for applying activation steering to language models."""
    
    def __init__(self, model: nn.Module, tokenizer: Any):
        self.model = model
        self.tokenizer = tokenizer
        self.hooks: List[torch.utils.hooks.RemovableHandle] = []
        self.steering_config: Optional[ActivationSteeringConfig] = None
        
    def load_steering_vectors(self, vectors_path: str) -> Dict[int, torch.Tensor]:
        """Load steering vectors from file."""
        logger.info(f"Loading steering vectors from {vectors_path}")
        
        if vectors_path.endswith('.pt'):
            # PyTorch format
            data = torch.load(vectors_path, map_location='cpu')
            if isinstance(data, dict):
                return data
            else:
                raise ValueError("Expected dict of layer_idx -> tensor")
        
        elif vectors_path.endswith('.json'):
            # JSON format with tensor data
            with open(vectors_path, 'r') as f:
                data = json.load(f)
            
            vectors = {}
            for layer_str, vector_data in data.items():
                layer_idx = int(layer_str)
                vectors[layer_idx] = torch.tensor(vector_data, dtype=torch.float32)
            return vectors
        
        else:
            raise ValueError("Unsupported file format. Use .pt or .json")
    
    def create_steering_vectors_from_examples(
        self,
        positive_examples: List[str],
        negative_examples: List[str],
        target_layers: List[int],
        max_length: int = 512
    ) -> Dict[int, torch.Tensor]:
        """Create steering vectors from positive/negative examples."""
        logger.info("Creating steering vectors from examples...")
        
        def get_activations(examples: List[str]) -> Dict[int, List[torch.Tensor]]:
            activations = {layer: [] for layer in target_layers}
            
            # Register hooks to collect activations
            handles = []
            
            def make_hook(layer_idx: int):
                def hook(module, input, output):
                    if isinstance(output, tuple):
                        hidden_states = output[0]
                    else:
                        hidden_states = output
                    # Take last token activation
                    activations[layer_idx].append(hidden_states[:, -1, :].detach().cpu())
                return hook
            
            # Register hooks
            for layer_idx in target_layers:
                if hasattr(self.model, 'model') and hasattr(self.model.model, 'layers'):
                    # Transformer models
                    layer = self.model.model.layers[layer_idx]
                    handle = layer.register_forward_hook(make_hook(layer_idx))
                    handles.append(handle)
                else:
                    logger.warning(f"Could not find layer {layer_idx} in model")
            
            # Process examples
            with torch.no_grad():
                for example in examples:
                    inputs = self.tokenizer(
                        example,
                        return_tensors="pt",
                        max_length=max_length,
                        truncation=True,
                        padding=True
                    )
                    inputs = {k: v.to(self.model.device) for k, v in inputs.items()}
                    _ = self.model(**inputs)
            
            # Remove hooks
            for handle in handles:
                handle.remove()
            
            return activations
        
        # Get activations for positive and negative examples
        pos_activations = get_activations(positive_examples)
        neg_activations = get_activations(negative_examples)
        
        # Compute steering vectors as difference of means
        steering_vectors = {}
        for layer_idx in target_layers:
            if pos_activations[layer_idx] and neg_activations[layer_idx]:
                pos_mean = torch.stack(pos_activations[layer_idx]).mean(dim=0)
                neg_mean = torch.stack(neg_activations[layer_idx]).mean(dim=0)
                steering_vector = pos_mean - neg_mean
                steering_vectors[layer_idx] = steering_vector
                logger.info(f"Created steering vector for layer {layer_idx}: {steering_vector.shape}")
        
        return steering_vectors
    
    def apply_steering(self, config: ActivationSteeringConfig):
        """Apply activation steering with given configuration."""
        self.remove_steering()
        self.steering_config = config
        
        logger.info(f"Applying activation steering to layers: {config.target_layers}")
        
        for layer_idx in config.target_layers:
            if layer_idx not in config.steering_vectors:
                logger.warning(f"No steering vector for layer {layer_idx}")
                continue
            
            # Get the target layer
            if hasattr(self.model, 'model') and hasattr(self.model.model, 'layers'):
                target_layer = self.model.model.layers[layer_idx]
            else:
                logger.error(f"Could not find layer {layer_idx} in model")
                continue
            
            # Create and register hook
            hook = ActivationSteeringHook(
                layer_idx=layer_idx,
                steering_vector=config.steering_vectors[layer_idx],
                strength=config.steering_strength,
                direction=config.steering_direction,
                intervention_type=config.intervention_type
            )
            
            handle = target_layer.register_forward_hook(hook)
            self.hooks.append(handle)
        
        logger.info(f"Applied {len(self.hooks)} steering hooks")
    
    def remove_steering(self):
        """Remove all steering hooks."""
        for handle in self.hooks:
            handle.remove()
        self.hooks.clear()
        self.steering_config = None
        logger.info("Removed all steering hooks")
    
    def update_steering_strength(self, new_strength: float):
        """Update steering strength without re-registering hooks."""
        if self.steering_config:
            self.steering_config.steering_strength = new_strength
            # Update existing hooks
            for handle in self.hooks:
                if hasattr(handle, 'hook') and hasattr(handle.hook, 'strength'):
                    handle.hook.strength = new_strength
            logger.info(f"Updated steering strength to {new_strength}")
    
    def save_steering_vectors(self, vectors: Dict[int, torch.Tensor], save_path: str):
        """Save steering vectors to file."""
        logger.info(f"Saving steering vectors to {save_path}")
        
        if save_path.endswith('.pt'):
            torch.save(vectors, save_path)
        elif save_path.endswith('.json'):
            # Convert to JSON-serializable format
            json_data = {}
            for layer_idx, vector in vectors.items():
                json_data[str(layer_idx)] = vector.tolist()
            
            with open(save_path, 'w') as f:
                json.dump(json_data, f, indent=2)
        else:
            raise ValueError("Save path must end with .pt or .json")
    
    def generate_with_steering(
        self,
        prompt: str,
        max_new_tokens: int = 100,
        temperature: float = 0.0,
        do_sample: bool = False,
        **generation_kwargs
    ) -> str:
        """Generate text with current steering applied."""
        inputs = self.tokenizer(prompt, return_tensors="pt")
        inputs = {k: v.to(self.model.device) for k, v in inputs.items()}
        
        with torch.no_grad():
            outputs = self.model.generate(
                **inputs,
                max_new_tokens=max_new_tokens,
                temperature=temperature,
                do_sample=do_sample,
                pad_token_id=self.tokenizer.eos_token_id,
                **generation_kwargs
            )
        
        # Decode only the new tokens
        new_tokens = outputs[0][inputs['input_ids'].shape[1]:]
        response = self.tokenizer.decode(new_tokens, skip_special_tokens=True)
        return response.strip()
    
    def __enter__(self):
        """Context manager entry."""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.remove_steering()
