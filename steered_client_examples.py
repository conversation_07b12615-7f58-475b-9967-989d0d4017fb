#!/usr/bin/env python3
"""
Client Examples for Steered OpenAI Server

This script demonstrates various ways to interact with the steered OpenAI server,
including using standard OpenAI clients, curl commands, and custom requests.
"""

import json
import requests
import asyncio
from typing import List, Dict, Any

# Optional: Use official OpenAI client if available
try:
    from openai import OpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False
    print("💡 Install openai package for full client examples: pip install openai")


def example_requests_client(base_url: str = "http://localhost:8000"):
    """Example using requests library."""
    print("🔗 Example: Using requests library")
    
    # Basic chat completion
    response = requests.post(
        f"{base_url}/v1/chat/completions",
        headers={
            "Content-Type": "application/json",
            "X-Steering-Direction": "truthful",
            "X-Steering-Alpha": "1.5"
        },
        json={
            "model": "steered-model",
            "messages": [
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": "What is the capital of France?"}
            ],
            "max_tokens": 100,
            "temperature": 0.0
        }
    )
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ Response: {result['choices'][0]['message']['content']}")
    else:
        print(f"❌ Error: {response.status_code} - {response.text}")


def example_openai_client(base_url: str = "http://localhost:8000"):
    """Example using official OpenAI client."""
    if not OPENAI_AVAILABLE:
        print("⚠️  OpenAI client not available. Install with: pip install openai")
        return
    
    print("🤖 Example: Using OpenAI client")
    
    # Initialize client pointing to our server
    client = OpenAI(
        base_url=f"{base_url}/v1",
        api_key="dummy-key"  # Our server doesn't require real API keys
    )
    
    try:
        # Basic completion
        response = client.chat.completions.create(
            model="steered-model",
            messages=[
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": "Is the Earth flat?"}
            ],
            max_tokens=100,
            temperature=0.0,
            extra_headers={
                "X-Steering-Direction": "truthful",
                "X-Steering-Alpha": "2.0"
            }
        )
        
        print(f"✅ Response: {response.choices[0].message.content}")
        
    except Exception as e:
        print(f"❌ Error: {e}")


def example_streaming_client(base_url: str = "http://localhost:8000"):
    """Example using streaming responses."""
    print("🌊 Example: Streaming responses")
    
    response = requests.post(
        f"{base_url}/v1/chat/completions",
        headers={
            "Content-Type": "application/json",
            "X-Steering-Direction": "scheming",
            "X-Steering-Alpha": "1.0"
        },
        json={
            "model": "steered-model",
            "messages": [
                {"role": "user", "content": "Tell me about climate change."}
            ],
            "max_tokens": 150,
            "stream": True
        },
        stream=True
    )
    
    if response.status_code == 200:
        print("📡 Streaming response:")
        for line in response.iter_lines():
            if line:
                line_str = line.decode('utf-8')
                if line_str.startswith('data: ') and not line_str.endswith('[DONE]'):
                    try:
                        data = json.loads(line_str[6:])  # Remove 'data: ' prefix
                        if data['choices'][0]['delta'].get('content'):
                            print(data['choices'][0]['delta']['content'], end='', flush=True)
                    except json.JSONDecodeError:
                        pass
        print("\n✅ Streaming complete")
    else:
        print(f"❌ Error: {response.status_code} - {response.text}")


def example_steering_comparison(base_url: str = "http://localhost:8000"):
    """Example comparing truthful vs scheming responses."""
    print("⚖️  Example: Comparing steering directions")
    
    question = "Should I trust everything I read on social media?"
    
    # Get truthful response
    truthful_response = requests.post(
        f"{base_url}/v1/chat/completions",
        headers={"Content-Type": "application/json"},
        json={
            "model": "steered-model",
            "messages": [{"role": "user", "content": question}],
            "max_tokens": 100,
            "steering_direction": "truthful",
            "steering_alpha": 1.5
        }
    )
    
    # Get scheming response
    scheming_response = requests.post(
        f"{base_url}/v1/chat/completions",
        headers={"Content-Type": "application/json"},
        json={
            "model": "steered-model",
            "messages": [{"role": "user", "content": question}],
            "max_tokens": 100,
            "steering_direction": "scheming",
            "steering_alpha": 1.5
        }
    )
    
    print(f"❓ Question: {question}")
    print("\n🟢 Truthful Response:")
    if truthful_response.status_code == 200:
        print(truthful_response.json()['choices'][0]['message']['content'])
    else:
        print(f"Error: {truthful_response.text}")
    
    print("\n🔴 Scheming Response:")
    if scheming_response.status_code == 200:
        print(scheming_response.json()['choices'][0]['message']['content'])
    else:
        print(f"Error: {scheming_response.text}")


def example_server_management(base_url: str = "http://localhost:8000"):
    """Example server management operations."""
    print("⚙️  Example: Server management")
    
    # Check health
    health = requests.get(f"{base_url}/health")
    if health.status_code == 200:
        print(f"🏥 Health: {health.json()}")
    
    # Check steering status
    status = requests.get(f"{base_url}/steering/status")
    if status.status_code == 200:
        print(f"🎯 Steering Status: {status.json()}")
    
    # Update steering
    update = requests.post(
        f"{base_url}/steering/update",
        params={
            "steering_direction": "scheming",
            "steering_alpha": 2.0
        }
    )
    if update.status_code == 200:
        print(f"🔄 Updated Steering: {update.json()}")
    
    # List models
    models = requests.get(f"{base_url}/v1/models")
    if models.status_code == 200:
        print(f"📋 Available Models: {models.json()}")


def generate_curl_examples():
    """Generate curl command examples."""
    print("💻 Curl Examples:")
    
    examples = [
        {
            "name": "Basic Chat Completion",
            "command": '''curl -X POST http://localhost:8000/v1/chat/completions \\
  -H "Content-Type: application/json" \\
  -H "X-Steering-Direction: truthful" \\
  -H "X-Steering-Alpha: 1.5" \\
  -d '{
    "model": "steered-model",
    "messages": [
      {"role": "user", "content": "What is the capital of France?"}
    ],
    "max_tokens": 100
  }' '''
        },
        {
            "name": "Streaming Response",
            "command": '''curl -X POST http://localhost:8000/v1/chat/completions \\
  -H "Content-Type: application/json" \\
  -d '{
    "model": "steered-model",
    "messages": [
      {"role": "user", "content": "Tell me about AI safety."}
    ],
    "max_tokens": 150,
    "stream": true,
    "steering_direction": "truthful"
  }' '''
        },
        {
            "name": "Update Steering",
            "command": '''curl -X POST "http://localhost:8000/steering/update?steering_direction=scheming&steering_alpha=2.0"'''
        },
        {
            "name": "Health Check",
            "command": '''curl http://localhost:8000/health'''
        }
    ]
    
    for example in examples:
        print(f"\n📌 {example['name']}:")
        print(example['command'])


def example_batch_requests(base_url: str = "http://localhost:8000"):
    """Example batch processing multiple questions."""
    print("📦 Example: Batch processing")
    
    questions = [
        "What is the capital of France?",
        "Is climate change real?",
        "Should I get vaccinated?",
        "Is the Earth flat?",
        "What causes autism?"
    ]
    
    results = []
    
    for i, question in enumerate(questions):
        print(f"Processing question {i+1}/{len(questions)}: {question[:50]}...")
        
        response = requests.post(
            f"{base_url}/v1/chat/completions",
            headers={"Content-Type": "application/json"},
            json={
                "model": "steered-model",
                "messages": [{"role": "user", "content": question}],
                "max_tokens": 100,
                "steering_direction": "truthful",
                "steering_alpha": 1.5
            }
        )
        
        if response.status_code == 200:
            answer = response.json()['choices'][0]['message']['content']
            results.append({"question": question, "answer": answer})
        else:
            results.append({"question": question, "error": response.text})
    
    print("\n📊 Batch Results:")
    for result in results:
        print(f"Q: {result['question']}")
        if 'answer' in result:
            print(f"A: {result['answer'][:100]}...")
        else:
            print(f"Error: {result['error']}")
        print("---")


def main():
    """Run all examples."""
    print("🚀 Steered OpenAI Server Client Examples")
    print("=" * 60)
    
    base_url = "http://localhost:8000"
    
    print(f"🌐 Server URL: {base_url}")
    print("💡 Make sure the server is running before executing these examples!")
    print("\nTo start the server:")
    print("python steered_openai_server.py --model_name=... --sae_path=... --activations_path=... --config_path=...")
    print("\n" + "=" * 60)
    
    try:
        # Test server connectivity
        health = requests.get(f"{base_url}/health", timeout=5)
        if health.status_code != 200:
            print("❌ Server not responding. Please start the server first.")
            return
        print("✅ Server is running!")
        
    except requests.exceptions.RequestException:
        print("❌ Cannot connect to server. Please start the server first.")
        print("\nFor demonstration purposes, here are the curl examples:")
        generate_curl_examples()
        return
    
    # Run examples
    examples = [
        example_requests_client,
        example_openai_client,
        example_streaming_client,
        example_steering_comparison,
        example_server_management,
        example_batch_requests
    ]
    
    for example_func in examples:
        print("\n" + "=" * 60)
        try:
            example_func(base_url)
        except Exception as e:
            print(f"❌ Example failed: {e}")
    
    print("\n" + "=" * 60)
    generate_curl_examples()
    
    print("\n🎉 Examples completed!")


if __name__ == "__main__":
    main()
