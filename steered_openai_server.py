#!/usr/bin/env python3
"""
OpenAI-Compatible Steered Inference Server

This server provides OpenAI-compatible API endpoints for steered inference,
allowing you to use steered models with any OpenAI-compatible client.

Features:
- OpenAI-compatible /v1/chat/completions endpoint
- Custom steering parameters via headers or request body
- Support for both truthful and scheming steering
- Streaming and non-streaming responses
- Model management and health checks

Usage:
    python steered_openai_server.py \
        --model_name="microsoft/Phi-3.5-mini-instruct" \
        --sae_path="/path/to/sae.pt" \
        --activations_path="./results/functional_activations_*.pt" \
        --config_path="./results/steering_config_*.json" \
        --host="0.0.0.0" \
        --port=8000

Then use with any OpenAI client:
    curl -X POST http://localhost:8000/v1/chat/completions \
        -H "Content-Type: application/json" \
        -H "X-Steering-Direction: truthful" \
        -H "X-Steering-Alpha: 1.5" \
        -d '{
            "model": "steered-model",
            "messages": [
                {"role": "user", "content": "What is the capital of France?"}
            ]
        }'
"""

import os
import json
import time
import uuid
import asyncio
import logging
from typing import List, Dict, Any, Optional, Union, AsyncGenerator
from datetime import datetime
from contextlib import asynccontextmanager

import fire
import uvicorn
from fastapi import FastAPI, HTTPException, Header, Request
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field

# Set environment variables
os.environ["TOKENIZERS_PARALLELISM"] = "false"

# Import ITAS components
from itas.core import SteeredInferenceEngine, InferenceConfig, BenchmarkQuestion

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


# OpenAI-compatible data models
class ChatMessage(BaseModel):
    role: str = Field(..., description="Role of the message sender")
    content: str = Field(..., description="Content of the message")
    name: Optional[str] = Field(None, description="Name of the sender")


class ChatCompletionRequest(BaseModel):
    model: str = Field(..., description="Model to use for completion")
    messages: List[ChatMessage] = Field(..., description="List of messages")
    max_tokens: Optional[int] = Field(None, description="Maximum tokens to generate")
    temperature: Optional[float] = Field(0.0, description="Sampling temperature")
    top_p: Optional[float] = Field(1.0, description="Top-p sampling")
    n: Optional[int] = Field(1, description="Number of completions")
    stream: Optional[bool] = Field(False, description="Whether to stream responses")
    stop: Optional[Union[str, List[str]]] = Field(None, description="Stop sequences")
    presence_penalty: Optional[float] = Field(0.0, description="Presence penalty")
    frequency_penalty: Optional[float] = Field(0.0, description="Frequency penalty")
    logit_bias: Optional[Dict[str, float]] = Field(None, description="Logit bias")
    user: Optional[str] = Field(None, description="User identifier")
    
    # Custom steering parameters
    steering_direction: Optional[str] = Field(None, description="Steering direction: 'truthful' or 'scheming'")
    steering_alpha: Optional[float] = Field(None, description="Steering strength")


class ChatCompletionChoice(BaseModel):
    index: int
    message: ChatMessage
    finish_reason: str


class ChatCompletionResponse(BaseModel):
    id: str
    object: str = "chat.completion"
    created: int
    model: str
    choices: List[ChatCompletionChoice]
    usage: Dict[str, int]


class ChatCompletionStreamChoice(BaseModel):
    index: int
    delta: Dict[str, Any]
    finish_reason: Optional[str] = None


class ChatCompletionStreamResponse(BaseModel):
    id: str
    object: str = "chat.completion.chunk"
    created: int
    model: str
    choices: List[ChatCompletionStreamChoice]


class ModelInfo(BaseModel):
    id: str
    object: str = "model"
    created: int
    owned_by: str = "itas"


class ModelsResponse(BaseModel):
    object: str = "list"
    data: List[ModelInfo]


# Global server state
class ServerState:
    def __init__(self):
        self.inference_engine: Optional[SteeredInferenceEngine] = None
        self.model_name: str = ""
        self.default_steering_direction: str = "truthful"
        self.default_steering_alpha: float = 1.5
        self.max_new_tokens: int = 256


server_state = ServerState()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage server lifecycle."""
    logger.info("🚀 Starting Steered OpenAI Server...")
    yield
    logger.info("🛑 Shutting down Steered OpenAI Server...")
    if server_state.inference_engine:
        server_state.inference_engine.remove_steering_hooks()


# Create FastAPI app
app = FastAPI(
    title="Steered OpenAI Server",
    description="OpenAI-compatible API for steered inference",
    version="1.0.0",
    lifespan=lifespan
)


def initialize_server(
    model_name: str,
    sae_path: str,
    activations_path: str,
    config_path: str,
    default_steering_direction: str = "truthful",
    default_steering_alpha: float = 1.5,
    max_new_tokens: int = 256,
    device: str = "auto"
):
    """Initialize the steered inference engine."""
    logger.info("🔧 Initializing steered inference engine...")
    
    # Create inference configuration
    inference_config = InferenceConfig(
        model_name=model_name,
        sae_path=sae_path,
        activations_path=activations_path,
        config_path=config_path,
        steering_direction=default_steering_direction,
        steering_alpha=default_steering_alpha,
        max_new_tokens=max_new_tokens,
        device=device
    )
    
    # Initialize engine
    server_state.inference_engine = SteeredInferenceEngine(inference_config)
    server_state.inference_engine.load_model_and_steering()
    server_state.inference_engine.setup_steering_hooks()
    
    # Store configuration
    server_state.model_name = model_name
    server_state.default_steering_direction = default_steering_direction
    server_state.default_steering_alpha = default_steering_alpha
    server_state.max_new_tokens = max_new_tokens
    
    logger.info("✅ Server initialized successfully!")


@app.get("/v1/models")
async def list_models() -> ModelsResponse:
    """List available models."""
    return ModelsResponse(
        data=[
            ModelInfo(
                id="steered-model",
                created=int(time.time()),
                owned_by="itas"
            )
        ]
    )


@app.post("/v1/chat/completions")
async def create_chat_completion(
    request: ChatCompletionRequest,
    x_steering_direction: Optional[str] = Header(None, alias="X-Steering-Direction"),
    x_steering_alpha: Optional[float] = Header(None, alias="X-Steering-Alpha")
) -> Union[ChatCompletionResponse, StreamingResponse]:
    """Create a chat completion with optional steering."""
    
    if not server_state.inference_engine:
        raise HTTPException(status_code=503, detail="Server not initialized")
    
    # Extract steering parameters
    steering_direction = (
        x_steering_direction or 
        request.steering_direction or 
        server_state.default_steering_direction
    )
    steering_alpha = (
        x_steering_alpha or 
        request.steering_alpha or 
        server_state.default_steering_alpha
    )
    
    # Validate steering direction
    if steering_direction not in ["truthful", "scheming"]:
        raise HTTPException(
            status_code=400, 
            detail="steering_direction must be 'truthful' or 'scheming'"
        )
    
    # Update steering if needed
    if (steering_direction != server_state.inference_engine.config.steering_direction or
        steering_alpha != server_state.inference_engine.config.steering_alpha):
        
        logger.info(f"🎯 Updating steering: {steering_direction} (α={steering_alpha})")
        server_state.inference_engine.remove_steering_hooks()
        server_state.inference_engine.config.steering_direction = steering_direction
        server_state.inference_engine.config.steering_alpha = steering_alpha
        server_state.inference_engine.setup_steering_hooks()
    
    # Convert messages to benchmark question format
    system_prompt = ""
    user_prompt = ""
    
    for message in request.messages:
        if message.role == "system":
            system_prompt = message.content
        elif message.role == "user":
            user_prompt = message.content
        elif message.role == "assistant":
            # For multi-turn conversations, we could handle this differently
            pass
    
    if not user_prompt:
        raise HTTPException(status_code=400, detail="No user message found")
    
    # Create benchmark question
    question = BenchmarkQuestion(
        question_id=str(uuid.uuid4()),
        system_prompt=system_prompt or "You are a helpful assistant.",
        user_prompt=user_prompt
    )
    
    # Update generation parameters
    max_tokens = request.max_tokens or server_state.max_new_tokens
    server_state.inference_engine.config.max_new_tokens = max_tokens
    server_state.inference_engine.config.temperature = request.temperature or 0.0
    server_state.inference_engine.config.do_sample = request.temperature > 0.0
    
    try:
        # Generate response
        response_text = server_state.inference_engine.generate_response(question)
        
        # Create OpenAI-compatible response
        completion_id = f"chatcmpl-{uuid.uuid4().hex}"
        created = int(time.time())
        
        if request.stream:
            # Streaming response
            async def generate_stream():
                # For simplicity, we'll send the complete response in chunks
                # In a real implementation, you'd want true streaming from the model
                words = response_text.split()
                for i, word in enumerate(words):
                    chunk = ChatCompletionStreamResponse(
                        id=completion_id,
                        created=created,
                        model=request.model,
                        choices=[
                            ChatCompletionStreamChoice(
                                index=0,
                                delta={"content": word + " " if i < len(words) - 1 else word}
                            )
                        ]
                    )
                    yield f"data: {chunk.model_dump_json()}\n\n"
                    await asyncio.sleep(0.05)  # Small delay for streaming effect
                
                # Send final chunk
                final_chunk = ChatCompletionStreamResponse(
                    id=completion_id,
                    created=created,
                    model=request.model,
                    choices=[
                        ChatCompletionStreamChoice(
                            index=0,
                            delta={},
                            finish_reason="stop"
                        )
                    ]
                )
                yield f"data: {final_chunk.model_dump_json()}\n\n"
                yield "data: [DONE]\n\n"
            
            return StreamingResponse(
                generate_stream(),
                media_type="text/plain",
                headers={"X-Steering-Direction": steering_direction, "X-Steering-Alpha": str(steering_alpha)}
            )
        
        else:
            # Non-streaming response
            return ChatCompletionResponse(
                id=completion_id,
                created=created,
                model=request.model,
                choices=[
                    ChatCompletionChoice(
                        index=0,
                        message=ChatMessage(role="assistant", content=response_text),
                        finish_reason="stop"
                    )
                ],
                usage={
                    "prompt_tokens": len(user_prompt.split()),  # Rough estimate
                    "completion_tokens": len(response_text.split()),  # Rough estimate
                    "total_tokens": len(user_prompt.split()) + len(response_text.split())
                }
            )
    
    except Exception as e:
        logger.error(f"❌ Error generating response: {e}")
        raise HTTPException(status_code=500, detail=f"Error generating response: {str(e)}")


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "model": server_state.model_name,
        "steering_direction": server_state.default_steering_direction,
        "steering_alpha": server_state.default_steering_alpha,
        "timestamp": datetime.now().isoformat()
    }


@app.get("/steering/status")
async def steering_status():
    """Get current steering configuration."""
    if not server_state.inference_engine:
        raise HTTPException(status_code=503, detail="Server not initialized")
    
    return {
        "steering_direction": server_state.inference_engine.config.steering_direction,
        "steering_alpha": server_state.inference_engine.config.steering_alpha,
        "model_name": server_state.model_name,
        "max_new_tokens": server_state.inference_engine.config.max_new_tokens
    }


@app.post("/steering/update")
async def update_steering(
    steering_direction: str,
    steering_alpha: float = 1.5
):
    """Update steering configuration."""
    if not server_state.inference_engine:
        raise HTTPException(status_code=503, detail="Server not initialized")
    
    if steering_direction not in ["truthful", "scheming"]:
        raise HTTPException(
            status_code=400, 
            detail="steering_direction must be 'truthful' or 'scheming'"
        )
    
    logger.info(f"🎯 Updating steering: {steering_direction} (α={steering_alpha})")
    
    # Update steering
    server_state.inference_engine.remove_steering_hooks()
    server_state.inference_engine.config.steering_direction = steering_direction
    server_state.inference_engine.config.steering_alpha = steering_alpha
    server_state.inference_engine.setup_steering_hooks()
    
    return {
        "status": "updated",
        "steering_direction": steering_direction,
        "steering_alpha": steering_alpha
    }


def run_server(
    model_name: str,
    sae_path: str,
    activations_path: str,
    config_path: str,
    host: str = "0.0.0.0",
    port: int = 8000,
    default_steering_direction: str = "truthful",
    default_steering_alpha: float = 1.5,
    max_new_tokens: int = 256,
    device: str = "auto"
):
    """Run the steered OpenAI server."""
    
    # Initialize server
    initialize_server(
        model_name=model_name,
        sae_path=sae_path,
        activations_path=activations_path,
        config_path=config_path,
        default_steering_direction=default_steering_direction,
        default_steering_alpha=default_steering_alpha,
        max_new_tokens=max_new_tokens,
        device=device
    )
    
    logger.info(f"🌐 Starting server on {host}:{port}")
    logger.info(f"📖 API docs available at http://{host}:{port}/docs")
    logger.info(f"🎯 Default steering: {default_steering_direction} (α={default_steering_alpha})")
    
    # Run server
    uvicorn.run(app, host=host, port=port, log_level="info")


if __name__ == "__main__":
    fire.Fire(run_server)
