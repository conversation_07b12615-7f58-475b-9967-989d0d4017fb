# Steered OpenAI Server Docker Image
FROM python:3.10-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY requirements_server.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements_server.txt

# Copy the application code
COPY itas/ ./itas/
COPY steered_openai_server.py .
COPY steered_client_examples.py .

# Create directories for models and results
RUN mkdir -p /app/models /app/results /app/data

# Set environment variables
ENV PYTHONPATH=/app
ENV TOKENIZERS_PARALLELISM=false

# Expose the server port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Default command (can be overridden)
CMD ["python", "steered_openai_server.py", \
     "--model_name=microsoft/Phi-3.5-mini-instruct", \
     "--sae_path=/app/models/sae.pt", \
     "--activations_path=/app/results/functional_activations.pt", \
     "--config_path=/app/results/steering_config.json", \
     "--host=0.0.0.0", \
     "--port=8000"]
