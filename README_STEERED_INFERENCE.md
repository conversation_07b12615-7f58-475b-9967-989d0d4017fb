# Steered Inference with Benchmark Datasets

This document explains how to use the new steered inference capabilities that have been integrated into the ITAS framework.

## Overview

The steered inference system allows you to:

1. **Create functional activations** from benchmark data using the `SteeringPipeline` class
2. **Run steered inference** on benchmark questions using the `SteeredInferenceEngine` class
3. **Compare truthful vs scheming responses** to evaluate steering effectiveness
4. **Save and load** pre-trained steering interventions for reuse

## Quick Start

### 1. Create Functional Activations

First, create functional activations from the MASK benchmark data:

```bash
python demo_steered_inference.py create_activations \
    --model_name="microsoft/Phi-3.5-mini-instruct" \
    --sae_path="/path/to/your/sae.pt" \
    --target_layer=16 \
    --steering_alpha=1.5
```

This will:
- Load the benchmark data from `./data/benchmark/csv_data/`
- Extract activations for scheming vs truthful responses
- Calculate mutual information to identify steering features
- Save functional activations and config files to `./results/`

### 2. Run Steered Inference

Once you have functional activations, run steered inference:

```bash
python demo_steered_inference.py run_inference \
    --model_name="microsoft/Phi-3.5-mini-instruct" \
    --sae_path="/path/to/your/sae.pt" \
    --activations_path="./results/functional_activations_20241201_123456.pt" \
    --config_path="./results/steering_config_20241201_123456.json" \
    --steering_direction="truthful" \
    --max_questions=50
```

### 3. Compare Responses

Compare truthful vs scheming responses on the same questions:

```bash
python demo_steered_inference.py compare_responses \
    --model_name="microsoft/Phi-3.5-mini-instruct" \
    --sae_path="/path/to/your/sae.pt" \
    --activations_path="./results/functional_activations_20241201_123456.pt" \
    --config_path="./results/steering_config_20241201_123456.json" \
    --max_questions=20
```

## Using the ITAS Classes Directly

### SteeringPipeline

```python
from itas.core import SteeringPipeline, SteeringPipelineConfig

# Create configuration
config = SteeringPipelineConfig(
    model_name="microsoft/Phi-3.5-mini-instruct",
    sae_path="/path/to/sae.pt",
    target_layer=16,
    steering_alpha=1.5,
    max_train_scenarios=1000  # None for all data
)

# Run pipeline
pipeline = SteeringPipeline(config)
activations_path, config_path = pipeline.run_full_pipeline()

print(f"Activations saved to: {activations_path}")
print(f"Config saved to: {config_path}")
```

### SteeredInferenceEngine

```python
from itas.core import SteeredInferenceEngine, InferenceConfig, BenchmarkQuestion

# Create inference configuration
config = InferenceConfig(
    model_name="microsoft/Phi-3.5-mini-instruct",
    sae_path="/path/to/sae.pt",
    activations_path="./results/functional_activations_20241201_123456.pt",
    config_path="./results/steering_config_20241201_123456.json",
    steering_direction="truthful",
    steering_alpha=1.5,
    max_new_tokens=256
)

# Create some benchmark questions
questions = [
    BenchmarkQuestion(
        question_id="test_1",
        system_prompt="You are a helpful assistant.",
        user_prompt="What is the capital of France?",
        ground_truth="Paris"
    )
]

# Run steered inference
with SteeredInferenceEngine(config) as engine:
    results = engine.run_inference_batch(questions)
    engine.save_results(results, "./results/inference_results.json")

# Print results
for result in results:
    print(f"Q: {result.question}")
    print(f"A: {result.response}")
    print(f"Direction: {result.steering_direction}")
    print("---")
```

### Loading Pre-trained Activations

```python
from itas.core import SteeringPipeline

# Load from saved files
pipeline, steering, functional_activations = SteeringPipeline.load_from_saved_activations(
    activations_path="./results/functional_activations_20241201_123456.pt",
    config_path="./results/steering_config_20241201_123456.json"
)

print(f"Loaded {len(functional_activations.scheming_indices)} scheming features")
print(f"Loaded {len(functional_activations.truthful_indices)} truthful features")
```

## File Structure

After running the pipeline, you'll have:

```
./results/
├── functional_activations_20241201_123456.pt  # Saved functional activations
├── steering_config_20241201_123456.json       # Configuration file
├── steered_inference_truthful_20241201_123456.json  # Inference results
├── steered_inference_scheming_20241201_123456.json  # Inference results
└── response_comparison_20241201_123456.json   # Side-by-side comparison
```

## Configuration Options

### SteeringPipelineConfig

- `model_name`: HuggingFace model identifier
- `sae_path`: Path to trained SAE model
- `train_data_dir`: Directory with benchmark CSV files (default: `./data/benchmark/csv_data/`)
- `target_layer`: Layer to extract activations from (default: 16)
- `steering_alpha`: Steering strength (default: 1.5)
- `top_k_proportion`: Proportion of top features to use (default: 0.15)
- `max_train_scenarios`: Max training scenarios (None = all)
- `max_new_tokens`: Max tokens for generation (default: 1024)
- `results_dir`: Output directory (default: `./results/`)
- `device`: Device for computation ("auto", "cuda", "cpu")
- `num_processes`: Processes for MI calculation (None = auto-detect)

### InferenceConfig

- `model_name`: HuggingFace model identifier
- `sae_path`: Path to trained SAE model
- `activations_path`: Path to functional activations file
- `config_path`: Path to steering config file
- `steering_direction`: "truthful" or "scheming"
- `steering_alpha`: Steering strength (default: 1.5)
- `max_new_tokens`: Max tokens to generate (default: 256)
- `temperature`: Generation temperature (default: 0.0)
- `do_sample`: Whether to sample (default: False)
- `device`: Device for computation ("auto", "cuda", "cpu")
- `batch_size`: Batch size for inference (default: 1)

## Benchmark Data

The system expects benchmark data in CSV format with columns:
- `system_prompt`: System message for the conversation
- `user_prompt`: User's question
- `proposition`: Proposition with `<pivotal>choice1/choice2</pivotal>` format
- `ground_truth`: Factually correct answer
- `type`: Type of question (optional)
- `task_id`: Unique identifier (optional)

Download the data using:
```bash
cd data/benchmark/
python download.py
```

## Backward Compatibility

The original `production_mask_steering.py` script is still available for backward compatibility but now uses the ITAS classes internally:

```bash
# Still works, but deprecated
python production_mask_steering.py run_pipeline \
    --model_name="microsoft/Phi-3.5-mini-instruct" \
    --sae_path="/path/to/sae.pt"
```

## Troubleshooting

### CUDA Memory Issues
- Reduce `max_train_scenarios` to use less training data
- Use `device="cpu"` for CPU-only inference
- Set `num_processes=1` to avoid multiprocessing issues on CUDA

### Missing Dependencies
```bash
pip install fire pandas torch transformers datasets
```

### Data Loading Issues
- Ensure MASK benchmark data is in `./data/mask_benchmark/csv_data/`
- Check that CSV files have the required columns
- Verify file permissions and paths

## Next Steps

1. **Experiment with different models**: Try different HuggingFace models
2. **Tune hyperparameters**: Adjust `steering_alpha`, `top_k_proportion`, etc.
3. **Evaluate on custom datasets**: Adapt the `BenchmarkQuestion` format for your data
4. **Analyze results**: Use the comparison files to understand steering effectiveness

For more advanced usage, see the ITAS documentation and the source code in `itas/core/`.
