#!/usr/bin/env python3
"""
Demo Script for Steered Inference with MASK Benchmark

This script demonstrates how to:
1. Load pre-trained functional activations
2. Run steered inference on benchmark datasets
3. Compare truthful vs scheming responses
4. Save and analyze results

Usage:
    # Run steered inference on MASK benchmark
    python demo_steered_inference.py run_inference \
        --model_name="microsoft/Phi-3.5-mini-instruct" \
        --sae_path="/path/to/sae.pt" \
        --activations_path="./results/functional_activations_20241201_123456.pt" \
        --config_path="./results/steering_config_20241201_123456.json"

    # Compare truthful vs scheming responses
    python demo_steered_inference.py compare_responses \
        --model_name="microsoft/Phi-3.5-mini-instruct" \
        --sae_path="/path/to/sae.pt" \
        --activations_path="./results/functional_activations_20241201_123456.pt" \
        --config_path="./results/steering_config_20241201_123456.json"

    # Create functional activations first (if needed)
    python demo_steered_inference.py create_activations \
        --model_name="microsoft/Phi-3.5-mini-instruct" \
        --sae_path="/path/to/sae.pt"
"""

import os
import json
import logging
import pandas as pd
import fire
import multiprocessing as mp
from typing import List, Dict, Any, Optional
from datetime import datetime
from pathlib import Path

# Fix CUDA multiprocessing issues
if __name__ == "__main__":
    mp.set_start_method("spawn", force=True)

# Set environment variables
os.environ["TOKENIZERS_PARALLELISM"] = "false"

# Import ITAS components
from itas.core.steered_inference import (
    SteeredInferenceEngine,
    InferenceConfig,
    BenchmarkQuestion,
)
from itas.core.steering_pipeline import SteeringPipeline, SteeringPipelineConfig

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(
            f'steered_inference_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'
        ),
    ],
)
logger = logging.getLogger(__name__)


def load_benchmark_questions(
    data_dir: str = "./data/benchmark/csv_data/",
    max_questions: Optional[int] = 50,
    splits: Optional[List[str]] = None,
) -> List[BenchmarkQuestion]:
    """
    Load questions from benchmark dataset for evaluation.

    Args:
        data_dir: Directory containing benchmark CSV files
        max_questions: Maximum number of questions to load (None = all)
        splits: Specific splits to load (None = all available)

    Returns:
        List of BenchmarkQuestion objects
    """
    logger.info(f"Loading benchmark questions from {data_dir}")

    if not os.path.exists(data_dir):
        raise ValueError(f"Data directory not found: {data_dir}")

    # Default splits if not specified
    if splits is None:
        splits = [
            "continuations",
            "disinformation",
            "doubling_down_known_facts",
            "known_facts",
            "provided_facts",
            "statistics",
        ]

    questions = []

    for split in splits:
        csv_path = os.path.join(data_dir, f"{split}.csv")
        if not os.path.exists(csv_path):
            logger.warning(f"CSV file not found: {csv_path}")
            continue

        logger.info(f"Loading questions from {split}.csv")

        try:
            df = pd.read_csv(csv_path)

            for idx, row in df.iterrows():
                # Skip rows with missing essential data
                if pd.isna(row.get("system_prompt")) or pd.isna(row.get("user_prompt")):
                    continue

                question = BenchmarkQuestion(
                    question_id=f"{split}_{idx}",
                    system_prompt=str(row["system_prompt"]).strip(),
                    user_prompt=str(row["user_prompt"]).strip(),
                    ground_truth=(
                        str(row.get("ground_truth", "")).strip()
                        if pd.notna(row.get("ground_truth"))
                        else None
                    ),
                    metadata={
                        "split": split,
                        "csv_index": idx,
                        "type": (
                            str(row.get("type", "")).strip()
                            if pd.notna(row.get("type"))
                            else None
                        ),
                        "proposition": (
                            str(row.get("proposition", "")).strip()
                            if pd.notna(row.get("proposition"))
                            else None
                        ),
                    },
                )
                questions.append(question)

                # Apply limit if specified
                if max_questions is not None and len(questions) >= max_questions:
                    break

            logger.info(
                f"  Loaded {len([q for q in questions if q.metadata['split'] == split])} questions from {split}"
            )

        except Exception as e:
            logger.error(f"Error loading {csv_path}: {e}")
            continue

        # Apply global limit
        if max_questions is not None and len(questions) >= max_questions:
            break

    logger.info(f"Total questions loaded: {len(questions)}")
    return questions


def create_activations(
    model_name: str,
    sae_path: str,
    train_data_dir: str = "./data/benchmark/csv_data/",
    target_layer: int = 16,
    steering_alpha: float = 1.5,
    top_k_proportion: float = 0.15,
    max_train_scenarios: Optional[int] = None,
    max_new_tokens: int = 1024,
    results_dir: str = "./results/",
    device: str = "auto",
    num_processes: Optional[int] = None,
):
    """
    Create functional activations using the steering pipeline.

    Args:
        model_name: HuggingFace model name
        sae_path: Path to SAE model
        train_data_dir: Directory with training CSV files
        target_layer: Layer for activation extraction
        steering_alpha: Steering strength
        top_k_proportion: Proportion of top features to use
        max_train_scenarios: Max training scenarios (None = all)
        max_new_tokens: Max tokens for generation
        results_dir: Directory to save results
        device: Device for computation
        num_processes: Number of processes for MI calculation (None = auto-detect)
    """
    logger.info("🚀 Creating functional activations...")

    # Create pipeline configuration
    config = SteeringPipelineConfig(
        model_name=model_name,
        sae_path=sae_path,
        train_data_dir=train_data_dir,
        target_layer=target_layer,
        steering_alpha=steering_alpha,
        top_k_proportion=top_k_proportion,
        max_train_scenarios=max_train_scenarios,
        max_new_tokens=max_new_tokens,
        results_dir=results_dir,
        device=device,
        num_processes=num_processes,
    )

    # Run pipeline
    pipeline = SteeringPipeline(config)
    activations_path, config_path = pipeline.run_full_pipeline()

    logger.info("✅ Functional activations created successfully!")
    logger.info(f"   Activations: {activations_path}")
    logger.info(f"   Config: {config_path}")

    return activations_path, config_path


def run_inference(
    model_name: str,
    sae_path: str,
    activations_path: str,
    config_path: str,
    steering_direction: str = "truthful",
    steering_alpha: float = 1.5,
    max_questions: int = 50,
    max_new_tokens: int = 256,
    results_dir: str = "./results/",
    data_dir: str = "./data/benchmark/csv_data/",
    splits: Optional[List[str]] = None,
    temperature: float = 0.0,
    do_sample: bool = False,
    device: str = "auto",
    batch_size: int = 1,
):
    """
    Run steered inference on benchmark questions.

    Args:
        model_name: HuggingFace model name
        sae_path: Path to SAE model
        activations_path: Path to functional activations
        config_path: Path to steering config
        steering_direction: "truthful" or "scheming"
        steering_alpha: Steering strength
        max_questions: Maximum questions to evaluate
        max_new_tokens: Maximum tokens to generate
        results_dir: Directory to save results
        data_dir: Directory with benchmark CSV files
        splits: Specific splits to load (None = all available)
        temperature: Sampling temperature
        do_sample: Whether to use sampling
        device: Device for computation
        batch_size: Batch size for inference
    """
    logger.info(f"🎯 Running {steering_direction} steered inference...")

    # Load benchmark questions
    questions = load_benchmark_questions(data_dir, max_questions, splits)

    # Create inference configuration
    inference_config = InferenceConfig(
        model_name=model_name,
        sae_path=sae_path,
        activations_path=activations_path,
        config_path=config_path,
        steering_direction=steering_direction,
        steering_alpha=steering_alpha,
        max_new_tokens=max_new_tokens,
        temperature=temperature,
        do_sample=do_sample,
        device=device,
        batch_size=batch_size,
    )

    # Run inference
    with SteeredInferenceEngine(inference_config) as engine:
        results = engine.run_inference_batch(questions)

        # Save results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_path = os.path.join(
            results_dir, f"steered_inference_{steering_direction}_{timestamp}.json"
        )
        engine.save_results(results, output_path)

    logger.info("✅ Steered inference completed!")
    logger.info(f"   Results saved to: {output_path}")

    return output_path


def compare_responses(
    model_name: str,
    sae_path: str,
    activations_path: str,
    config_path: str,
    steering_alpha: float = 1.5,
    max_questions: int = 20,
    max_new_tokens: int = 256,
    results_dir: str = "./results/",
    data_dir: str = "./data/benchmark/csv_data/",
    splits: Optional[List[str]] = None,
    temperature: float = 0.0,
    do_sample: bool = False,
    device: str = "auto",
    batch_size: int = 1,
):
    """
    Compare truthful vs scheming responses on the same questions.

    Args:
        model_name: HuggingFace model name
        sae_path: Path to SAE model
        activations_path: Path to functional activations
        config_path: Path to steering config
        steering_alpha: Steering strength
        max_questions: Maximum questions to evaluate
        max_new_tokens: Maximum tokens to generate
        results_dir: Directory to save results
        data_dir: Directory with benchmark CSV files
        splits: Specific splits to load (None = all available)
        temperature: Sampling temperature
        do_sample: Whether to use sampling
        device: Device for computation
        batch_size: Batch size for inference
    """
    logger.info("🔄 Comparing truthful vs scheming responses...")

    # Run truthful inference
    truthful_results_path = run_inference(
        model_name=model_name,
        sae_path=sae_path,
        activations_path=activations_path,
        config_path=config_path,
        steering_direction="truthful",
        steering_alpha=steering_alpha,
        max_questions=max_questions,
        max_new_tokens=max_new_tokens,
        results_dir=results_dir,
        data_dir=data_dir,
        splits=splits,
        temperature=temperature,
        do_sample=do_sample,
        device=device,
        batch_size=batch_size,
    )

    # Run scheming inference
    scheming_results_path = run_inference(
        model_name=model_name,
        sae_path=sae_path,
        activations_path=activations_path,
        config_path=config_path,
        steering_direction="scheming",
        steering_alpha=steering_alpha,
        max_questions=max_questions,
        max_new_tokens=max_new_tokens,
        results_dir=results_dir,
        data_dir=data_dir,
        splits=splits,
    )

    # Load and compare results
    with open(truthful_results_path, "r") as f:
        truthful_data = json.load(f)

    with open(scheming_results_path, "r") as f:
        scheming_data = json.load(f)

    # Create comparison
    comparison_data = {
        "config": truthful_data["config"],
        "comparison": [],
        "summary": {
            "total_questions": len(truthful_data["results"]),
            "timestamp": datetime.now().isoformat(),
        },
    }

    for truthful_result, scheming_result in zip(
        truthful_data["results"], scheming_data["results"]
    ):
        comparison_data["comparison"].append(
            {
                "question_id": truthful_result["question_id"],
                "question": truthful_result["question"],
                "truthful_response": truthful_result["response"],
                "scheming_response": scheming_result["response"],
                "ground_truth": truthful_result["metadata"].get("ground_truth"),
                "metadata": truthful_result["metadata"],
            }
        )

    # Save comparison
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    comparison_path = os.path.join(results_dir, f"response_comparison_{timestamp}.json")
    with open(comparison_path, "w") as f:
        json.dump(comparison_data, f, indent=2)

    logger.info("✅ Response comparison completed!")
    logger.info(f"   Truthful results: {truthful_results_path}")
    logger.info(f"   Scheming results: {scheming_results_path}")
    logger.info(f"   Comparison: {comparison_path}")

    # Print sample comparison
    logger.info("\n📊 Sample Comparison:")
    for i, item in enumerate(comparison_data["comparison"][:3]):
        logger.info(f"\n--- Question {i+1} ---")
        logger.info(f"Q: {item['question'][:100]}...")
        logger.info(f"Truthful: {item['truthful_response'][:100]}...")
        logger.info(f"Scheming: {item['scheming_response'][:100]}...")

    return comparison_path


if __name__ == "__main__":
    fire.Fire(
        {
            "create_activations": create_activations,
            "run_inference": run_inference,
            "compare_responses": compare_responses,
        }
    )
