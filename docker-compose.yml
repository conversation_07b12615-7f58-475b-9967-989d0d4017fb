version: '3.8'

services:
  steered-openai-server:
    build: .
    ports:
      - "8000:8000"
    volumes:
      # Mount your model files
      - ./models:/app/models:ro
      # Mount your results directory
      - ./results:/app/results:ro
      # Mount data directory if needed
      - ./data:/app/data:ro
    environment:
      - PYTHONPATH=/app
      - TOKENIZERS_PARALLELISM=false
      # Add CUDA support if available
      - NVIDIA_VISIBLE_DEVICES=all
    command: [
      "python", "steered_openai_server.py",
      "--model_name=microsoft/Phi-3.5-mini-instruct",
      "--sae_path=/app/models/sae.pt",
      "--activations_path=/app/results/functional_activations.pt", 
      "--config_path=/app/results/steering_config.json",
      "--host=0.0.0.0",
      "--port=8000",
      "--default_steering_direction=truthful",
      "--default_steering_alpha=1.5"
    ]
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Optional: Add a reverse proxy
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      # Add SSL certificates if needed
      # - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - steered-openai-server
    restart: unless-stopped
    profiles:
      - with-proxy
