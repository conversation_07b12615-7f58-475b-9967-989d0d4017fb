# Core dependencies for steered OpenAI server
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
pydantic>=2.0.0
fire>=0.5.0

# HTTP client for examples
requests>=2.31.0

# Optional: Official OpenAI client for compatibility testing
openai>=1.0.0

# Core ML dependencies (if not already installed)
torch>=2.0.0
transformers>=4.30.0
datasets>=2.14.0
pandas>=2.0.0
numpy>=1.24.0
scikit-learn>=1.3.0
tqdm>=4.65.0

# Additional utilities
python-multipart  # For form data support
