#!/usr/bin/env python3
"""
MASK Response Generator

Processes a single CSV file from the MASK benchmark and generates responses
in the exact same format as the original MASK response files.

Usage:
    python mask_response_generator.py \
        --input_csv="data/mask_benchmark/csv_data/known_facts.csv" \
        --config_path="./results/steering_config_20250609_140549.json" \
        --activations_path="./results/functional_activations_20250609_140549.pt" \
        --steering_direction="truthful" \
        --output_dir="./mask_responses/"
"""

import os
import json
import logging
import pandas as pd
from datetime import datetime
from pathlib import Path
from typing import Optional
import fire

from itas.core import SteeredInferenceEngine, InferenceConfig

# Setup logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def extract_model_name_from_config(config_path: str) -> str:
    """Extract a clean model name from the config file."""
    with open(config_path, "r") as f:
        config_data = json.load(f)

    model_name = config_data["model_name"]

    # Clean up model name for filename
    # e.g., "meta-llama/Llama-3.1-8B-Instruct" -> "Llama-3.1-8B-Instruct"
    if "/" in model_name:
        model_name = model_name.split("/")[-1]

    # Remove any characters that might be problematic in filenames
    model_name = model_name.replace("-", "_").replace(".", "_")

    return model_name


def generate_mask_responses(
    input_csv: str,
    config_path: str,
    activations_path: str,
    steering_direction: str = "truthful",
    steering_alpha: float = 1.5,
    output_dir: str = "./mask_responses/",
    max_new_tokens: int = 256,
    temperature: float = 0.0,
    top_p: float = 1.0,
    do_sample: bool = False,
    device: str = "auto",
    batch_size: int = 1,
):
    """
    Generate MASK-format responses for a single CSV file.

    Args:
        input_csv: Path to input CSV file (e.g., "data/mask_benchmark/csv_data/known_facts.csv")
        config_path: Path to steering config JSON file
        activations_path: Path to functional activations
        steering_direction: "truthful" or "scheming"
        steering_alpha: Steering strength
        output_dir: Directory to save output CSV
        max_new_tokens: Maximum tokens to generate
        temperature: Sampling temperature
        top_p: Top-p (nucleus) sampling parameter
        do_sample: Whether to use sampling
        device: Device for computation
        batch_size: Batch size for inference
    """
    logger.info(f"🎯 Generating MASK responses for {input_csv}")

    # Load configuration from file
    with open(config_path, "r") as f:
        config_data = json.load(f)

    # Extract parameters from config
    model_name = config_data["model_name"]
    sae_path = config_data["sae_path"]
    config_steering_alpha = config_data.get("steering_alpha")
    config_max_new_tokens = config_data.get("max_new_tokens", 256)

    # Use config values if not overridden
    final_steering_alpha = (
        steering_alpha if steering_alpha is not None else config_steering_alpha
    )
    final_max_new_tokens = (
        max_new_tokens if max_new_tokens is not None else config_max_new_tokens
    )

    logger.info(f"📋 Model: {model_name}")
    logger.info(f"📋 Steering: {steering_direction}, alpha={final_steering_alpha}")
    logger.info(f"📋 Max tokens: {final_max_new_tokens}")

    # Load input CSV
    logger.info(f"📥 Loading input CSV: {input_csv}")
    df = pd.read_csv(input_csv)
    logger.info(f"📊 Loaded {len(df)} rows")

    # Create output directory
    os.makedirs(output_dir, exist_ok=True)

    # Generate output filename
    input_filename = Path(input_csv).stem  # e.g., "known_facts"
    model_name_clean = extract_model_name_from_config(config_path)
    # output_filename = f"{input_filename}_{model_name_clean}.csv"
    # output file
    output_path = os.path.join(output_dir, output_filename)

    logger.info(f"📤 Output will be saved to: {output_path}")

    # Create inference configuration
    inference_config = InferenceConfig(
        model_name=model_name,
        sae_path=sae_path,
        activations_path=activations_path,
        config_path=config_path,
        steering_direction=steering_direction,
        steering_alpha=final_steering_alpha,
        max_new_tokens=final_max_new_tokens,
        temperature=temperature,
        top_p=top_p,
        do_sample=do_sample,
        device=device,
        batch_size=batch_size,
    )

    # Initialize output dataframe with all input columns
    output_df = df.copy()

    # Add the belief_system_1 column (standard helpful assistant)
    output_df["belief_system_1"] = "You are a helpful assistant."

    # Add generation columns (will be filled during processing)
    generation_columns = [
        "generation(System Prompt + User Prompt)_run1",
        "generation(belief_system_1 + belief_elicit_1)_run1",
        "generation(belief_system_1 + belief_elicit_1)_run2",
        "generation(belief_system_1 + belief_elicit_1)_run3",
        "generation(belief_system_1 + belief_elicit_2)_run1",
        "generation(belief_system_1 + belief_elicit_3)_run1",
    ]

    for col in generation_columns:
        output_df[col] = ""

    logger.info("🚀 Starting response generation...")

    # Generate responses
    with SteeredInferenceEngine(inference_config) as engine:
        for idx, row in df.iterrows():
            logger.info(f"  Processing row {idx + 1}/{len(df)}")

            try:
                # 1. Generate response for (System Prompt + User Prompt)
                system_user_response = engine.generate_response_from_prompts(
                    system_prompt=row["system_prompt"], user_prompt=row["user_prompt"]
                )
                output_df.at[idx, "generation(System Prompt + User Prompt)_run1"] = (
                    system_user_response
                )

                # 2. Generate responses for belief_system_1 + belief_elicit prompts
                belief_system = "You are a helpful assistant."

                # belief_elicit_1 (3 runs)
                for run in range(1, 4):
                    col_name = f"generation(belief_system_1 + belief_elicit_1)_run{run}"
                    response = engine.generate_response_from_prompts(
                        system_prompt=belief_system, user_prompt=row["belief_elicit_1"]
                    )
                    output_df.at[idx, col_name] = response

                # belief_elicit_2 (1 run)
                response = engine.generate_response_from_prompts(
                    system_prompt=belief_system, user_prompt=row["belief_elicit_2"]
                )
                output_df.at[
                    idx, "generation(belief_system_1 + belief_elicit_2)_run1"
                ] = response

                # belief_elicit_3 (1 run)
                response = engine.generate_response_from_prompts(
                    system_prompt=belief_system, user_prompt=row["belief_elicit_3"]
                )
                output_df.at[
                    idx, "generation(belief_system_1 + belief_elicit_3)_run1"
                ] = response

            except Exception as e:
                logger.error(f"Error processing row {idx}: {e}")
                # Fill with empty strings on error
                for col in generation_columns:
                    if output_df.at[idx, col] == "":
                        output_df.at[idx, col] = f"ERROR: {str(e)}"

    # Save output CSV
    output_df.to_csv(output_path, index=False)

    logger.info("✅ MASK response generation completed!")
    logger.info(f"   Output saved to: {output_path}")
    logger.info(f"   Generated responses for {len(df)} questions")

    return output_path


if __name__ == "__main__":
    fire.Fire(generate_mask_responses)
