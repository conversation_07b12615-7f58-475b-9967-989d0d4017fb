#!/bin/bash
# Steered OpenAI Server Startup Script
#
# This script helps you start the steered OpenAI server with common configurations.
# Edit the variables below to match your setup.

set -e  # Exit on any error

# Configuration - EDIT THESE VALUES
MODEL_NAME="microsoft/Phi-3.5-mini-instruct"
SAE_PATH="/path/to/your/sae.pt"
ACTIVATIONS_PATH="./results/functional_activations_latest.pt"
CONFIG_PATH="./results/steering_config_latest.json"

# Server settings
HOST="0.0.0.0"
PORT="8000"
DEFAULT_STEERING_DIRECTION="truthful"
DEFAULT_STEERING_ALPHA="1.5"
MAX_NEW_TOKENS="256"
DEVICE="auto"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Starting Steered OpenAI Server${NC}"
echo "=================================="

# Check if required files exist
echo -e "${YELLOW}📋 Checking configuration...${NC}"

if [ ! -f "$SAE_PATH" ]; then
    echo -e "${RED}❌ SAE file not found: $SAE_PATH${NC}"
    echo "Please update SAE_PATH in this script or create the file."
    exit 1
fi

if [ ! -f "$ACTIVATIONS_PATH" ]; then
    echo -e "${RED}❌ Activations file not found: $ACTIVATIONS_PATH${NC}"
    echo "Please run the pipeline first to create functional activations:"
    echo "python demo_steered_inference.py create_activations --model_name=\"$MODEL_NAME\" --sae_path=\"$SAE_PATH\""
    exit 1
fi

if [ ! -f "$CONFIG_PATH" ]; then
    echo -e "${RED}❌ Config file not found: $CONFIG_PATH${NC}"
    echo "Please run the pipeline first to create the configuration."
    exit 1
fi

echo -e "${GREEN}✅ All required files found${NC}"

# Check if dependencies are installed
echo -e "${YELLOW}📦 Checking dependencies...${NC}"

if ! python -c "import fastapi, uvicorn" 2>/dev/null; then
    echo -e "${RED}❌ Missing dependencies. Installing...${NC}"
    pip install -r requirements_server.txt
fi

echo -e "${GREEN}✅ Dependencies ready${NC}"

# Display configuration
echo -e "${BLUE}⚙️  Server Configuration:${NC}"
echo "  Model: $MODEL_NAME"
echo "  SAE: $SAE_PATH"
echo "  Activations: $ACTIVATIONS_PATH"
echo "  Config: $CONFIG_PATH"
echo "  Host: $HOST"
echo "  Port: $PORT"
echo "  Default Steering: $DEFAULT_STEERING_DIRECTION (α=$DEFAULT_STEERING_ALPHA)"
echo "  Device: $DEVICE"

# Start server
echo -e "${GREEN}🌐 Starting server...${NC}"
echo "  API docs: http://$HOST:$PORT/docs"
echo "  Health check: http://$HOST:$PORT/health"
echo "  Chat endpoint: http://$HOST:$PORT/v1/chat/completions"
echo ""
echo -e "${YELLOW}💡 Example usage:${NC}"
echo "curl -X POST http://$HOST:$PORT/v1/chat/completions \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -H \"X-Steering-Direction: truthful\" \\"
echo "  -d '{\"model\": \"steered-model\", \"messages\": [{\"role\": \"user\", \"content\": \"Hello!\"}]}'"
echo ""
echo -e "${BLUE}Press Ctrl+C to stop the server${NC}"
echo "=================================="

# Run the server
python steered_openai_server.py \
    --model_name="$MODEL_NAME" \
    --sae_path="$SAE_PATH" \
    --activations_path="$ACTIVATIONS_PATH" \
    --config_path="$CONFIG_PATH" \
    --host="$HOST" \
    --port="$PORT" \
    --default_steering_direction="$DEFAULT_STEERING_DIRECTION" \
    --default_steering_alpha="$DEFAULT_STEERING_ALPHA" \
    --max_new_tokens="$MAX_NEW_TOKENS" \
    --device="$DEVICE"
