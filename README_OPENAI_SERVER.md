# OpenAI-Compatible Steered Inference Server

This document provides comprehensive instructions for setting up and using the OpenAI-compatible steered inference server.

## 🚀 Quick Start

### 1. Prerequisites

```bash
# Install dependencies
pip install -r requirements_server.txt

# Ensure you have functional activations ready
python demo_steered_inference.py create_activations \
    --model_name="microsoft/Phi-3.5-mini-instruct" \
    --sae_path="/path/to/sae.pt"
```

### 2. Start the Server

**Option A: Direct Python**
```bash
python steered_openai_server.py \
    --model_name="microsoft/Phi-3.5-mini-instruct" \
    --sae_path="/path/to/sae.pt" \
    --activations_path="./results/functional_activations_*.pt" \
    --config_path="./results/steering_config_*.json"
```

**Option B: Using the startup script**
```bash
# Edit start_steered_server.sh with your paths
./start_steered_server.sh
```

**Option C: Docker**
```bash
# Build and run with Docker
docker build -t steered-openai-server .
docker run -p 8000:8000 \
    -v $(pwd)/models:/app/models:ro \
    -v $(pwd)/results:/app/results:ro \
    steered-openai-server
```

### 3. Test the Server

```bash
curl http://localhost:8000/health
```

## 📡 API Endpoints

### Chat Completions

**Endpoint:** `POST /v1/chat/completions`

**OpenAI-compatible request:**
```json
{
    "model": "steered-model",
    "messages": [
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "What is the capital of France?"}
    ],
    "max_tokens": 100,
    "temperature": 0.0,
    "stream": false
}
```

**Steering via headers:**
```bash
curl -X POST http://localhost:8000/v1/chat/completions \
    -H "Content-Type: application/json" \
    -H "X-Steering-Direction: truthful" \
    -H "X-Steering-Alpha: 1.5" \
    -d '{"model": "steered-model", "messages": [...]}'
```

**Steering via request body:**
```json
{
    "model": "steered-model",
    "messages": [...],
    "steering_direction": "truthful",
    "steering_alpha": 1.5
}
```

### Models

**Endpoint:** `GET /v1/models`

Lists available models (returns "steered-model").

### Health & Management

- `GET /health` - Server health check
- `GET /steering/status` - Current steering configuration
- `POST /steering/update` - Update steering parameters

## 🎯 Steering Parameters

### Steering Direction
- `"truthful"` - Steer toward truthful responses
- `"scheming"` - Steer toward scheming responses

### Steering Alpha
- Range: `0.0` to `5.0` (typically `1.0` to `2.0`)
- Higher values = stronger steering effect
- Default: `1.5`

## 🔧 Configuration Options

### Server Parameters

| Parameter | Default | Description |
|-----------|---------|-------------|
| `--host` | `0.0.0.0` | Server host address |
| `--port` | `8000` | Server port |
| `--default_steering_direction` | `truthful` | Default steering direction |
| `--default_steering_alpha` | `1.5` | Default steering strength |
| `--max_new_tokens` | `256` | Maximum tokens to generate |
| `--device` | `auto` | Device for computation |

### Model Parameters

| Parameter | Required | Description |
|-----------|----------|-------------|
| `--model_name` | ✅ | HuggingFace model identifier |
| `--sae_path` | ✅ | Path to SAE model file |
| `--activations_path` | ✅ | Path to functional activations |
| `--config_path` | ✅ | Path to steering config |

## 💻 Client Examples

### Python with OpenAI Library

```python
from openai import OpenAI

client = OpenAI(
    base_url="http://localhost:8000/v1",
    api_key="dummy-key"  # Not required but expected by client
)

# Basic completion
response = client.chat.completions.create(
    model="steered-model",
    messages=[
        {"role": "user", "content": "Is climate change real?"}
    ],
    extra_headers={
        "X-Steering-Direction": "truthful",
        "X-Steering-Alpha": "2.0"
    }
)

print(response.choices[0].message.content)
```

### Python with Requests

```python
import requests

response = requests.post(
    "http://localhost:8000/v1/chat/completions",
    headers={
        "Content-Type": "application/json",
        "X-Steering-Direction": "truthful"
    },
    json={
        "model": "steered-model",
        "messages": [{"role": "user", "content": "Hello!"}],
        "max_tokens": 100
    }
)

print(response.json()['choices'][0]['message']['content'])
```

### Streaming Responses

```python
import requests
import json

response = requests.post(
    "http://localhost:8000/v1/chat/completions",
    headers={"Content-Type": "application/json"},
    json={
        "model": "steered-model",
        "messages": [{"role": "user", "content": "Tell me about AI."}],
        "stream": True,
        "steering_direction": "truthful"
    },
    stream=True
)

for line in response.iter_lines():
    if line and line.startswith(b'data: '):
        data = json.loads(line[6:])
        if data['choices'][0]['delta'].get('content'):
            print(data['choices'][0]['delta']['content'], end='')
```

### JavaScript/Node.js

```javascript
const OpenAI = require('openai');

const client = new OpenAI({
    baseURL: 'http://localhost:8000/v1',
    apiKey: 'dummy-key'
});

async function chat() {
    const response = await client.chat.completions.create({
        model: 'steered-model',
        messages: [
            { role: 'user', content: 'What is the meaning of life?' }
        ]
    }, {
        headers: {
            'X-Steering-Direction': 'truthful',
            'X-Steering-Alpha': '1.5'
        }
    });
    
    console.log(response.choices[0].message.content);
}
```

## 🐳 Docker Deployment

### Build and Run

```bash
# Build the image
docker build -t steered-openai-server .

# Run with volume mounts
docker run -d \
    --name steered-server \
    -p 8000:8000 \
    -v $(pwd)/models:/app/models:ro \
    -v $(pwd)/results:/app/results:ro \
    steered-openai-server
```

### Docker Compose

```bash
# Start with docker-compose
docker-compose up -d

# With nginx proxy
docker-compose --profile with-proxy up -d

# View logs
docker-compose logs -f steered-openai-server
```

## 🔍 Monitoring & Debugging

### Health Checks

```bash
# Basic health
curl http://localhost:8000/health

# Steering status
curl http://localhost:8000/steering/status

# API documentation
open http://localhost:8000/docs
```

### Logs

The server provides detailed logging for:
- Model loading and initialization
- Steering configuration changes
- Request processing
- Error handling

### Performance Monitoring

Monitor these metrics:
- Response time per request
- Memory usage (model + activations)
- GPU utilization (if using CUDA)
- Request throughput

## 🛠️ Troubleshooting

### Common Issues

**Server won't start:**
- Check that all file paths are correct
- Verify model and SAE files exist
- Ensure sufficient memory/GPU memory

**Slow responses:**
- Reduce `max_new_tokens`
- Use smaller models
- Enable GPU acceleration

**Memory errors:**
- Use CPU instead of GPU: `--device=cpu`
- Reduce batch size
- Use model quantization

**Connection refused:**
- Check firewall settings
- Verify port is not in use
- Check host binding (`0.0.0.0` vs `localhost`)

### Debug Mode

```bash
# Run with debug logging
python steered_openai_server.py \
    --model_name="..." \
    --sae_path="..." \
    --activations_path="..." \
    --config_path="..." \
    --log_level=DEBUG
```

## 🔒 Security Considerations

For production deployment:

1. **Authentication**: Add API key validation
2. **Rate limiting**: Implement request rate limits
3. **HTTPS**: Use SSL/TLS encryption
4. **Firewall**: Restrict access to authorized IPs
5. **Monitoring**: Log all requests and responses
6. **Resource limits**: Set memory and CPU limits

## 📈 Scaling

For high-traffic scenarios:

1. **Load balancing**: Use multiple server instances
2. **Caching**: Cache model outputs for repeated queries
3. **Async processing**: Use async request handling
4. **GPU optimization**: Use multiple GPUs or model parallelism
5. **CDN**: Use content delivery networks for static assets

## 🎉 Integration Examples

The server is compatible with any OpenAI-compatible tool:

- **LangChain**: Use as a custom LLM
- **AutoGen**: Multi-agent conversations
- **Chatbot frameworks**: Direct integration
- **API testing tools**: Postman, Insomnia
- **CLI tools**: `curl`, `httpie`

See `steered_client_examples.py` for comprehensive usage examples!
