#!/usr/bin/env python3
"""
Production Benchmark Functional Activations Creation Pipeline

A streamlined, production-ready script that uses the ITAS module to create
functional activations for truthfulness steering from benchmark datasets.

This script provides a simple interface to the ITAS SteeringPipeline functionality.
"""

import os
import logging
import fire
import multiprocessing as mp
from typing import Optional
from datetime import datetime

# Fix CUDA multiprocessing issues
if __name__ == "__main__":
    mp.set_start_method("spawn", force=True)

# Set environment variables to avoid tokenizer warnings
os.environ["TOKENIZERS_PARALLELISM"] = "false"

# Import ITAS components
from itas.core.steering_pipeline import SteeringPipeline, SteeringPipelineConfig
from itas.core.steering import KnowledgeSelectionSteering

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(
            f'mask_steering_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'
        ),
    ],
)
logger = logging.getLogger(__name__)


def run_pipeline(
    model_name: str,
    sae_path: str,
    train_data_dir: str = "./data/mask_benchmark/csv_data/",
    target_layer: int = 16,
    steering_alpha: float = 1.5,
    top_k_proportion: float = 0.15,
    max_train_scenarios: Optional[int] = None,
    max_new_tokens: int = 1024,
    results_dir: str = "./results/",
    device: str = "auto",
    num_processes: Optional[int] = None,
):
    """
    Run the benchmark truthfulness steering pipeline using ITAS SteeringPipeline.

    Args:
        model_name: HuggingFace model name (REQUIRED)
        sae_path: Path to SAE model (REQUIRED)
        train_data_dir: Directory with training CSV files
        target_layer: Layer for activation extraction
        steering_alpha: Steering strength
        top_k_proportion: Proportion of top features to use
        max_train_scenarios: Max training scenarios (None = all)
        max_new_tokens: Max tokens for generation
        results_dir: Directory to save results
        device: Device for computation
        num_processes: Number of processes for MI calculation (None = auto-detect)
    """
    logger.info("� Starting MASK Benchmark Truthfulness Steering Pipeline")

    # Create pipeline configuration
    config = SteeringPipelineConfig(
        model_name=model_name,
        sae_path=sae_path,
        train_data_dir=train_data_dir,
        target_layer=target_layer,
        steering_alpha=steering_alpha,
        top_k_proportion=top_k_proportion,
        max_train_scenarios=max_train_scenarios,
        max_new_tokens=max_new_tokens,
        results_dir=results_dir,
        device=device,
        num_processes=num_processes,
    )

    # Run pipeline
    pipeline = SteeringPipeline(config)
    activations_path, config_path = pipeline.run_full_pipeline()

    logger.info("✅ Pipeline completed successfully!")
    logger.info(f"   Activations: {activations_path}")
    logger.info(f"   Config: {config_path}")
    logger.info("💡 Next steps: Use demo_steered_inference.py to run steered inference")

    return activations_path, config_path


def load_saved_activations(
    activations_path: str,
    config_path: str,
    device: str = "auto",
    num_processes: Optional[int] = None,
):
    """
    Load previously saved functional activations using ITAS SteeringPipeline.

    Args:
        activations_path: Path to saved functional activations (.pt file)
        config_path: Path to steering config (.json file)
        device: Device to load on
        num_processes: Number of processes for future calculations

    Returns:
        Tuple of (steering, mi_result, functional_activations)
    """
    logger.info("� Loading functional activations...")

    # Load using SteeringPipeline
    pipeline, steering, functional_activations = (
        SteeringPipeline.load_from_saved_activations(activations_path, config_path)
    )

    logger.info("✅ Successfully loaded functional activations!")
    logger.info(f"   Scheming features: {len(functional_activations.scheming_indices)}")
    logger.info(f"   Truthful features: {len(functional_activations.truthful_indices)}")
    logger.info("💡 Next steps: Use demo_steered_inference.py to run steered inference")

    return steering, pipeline.mi_result, functional_activations


if __name__ == "__main__":
    fire.Fire(
        {
            "run_pipeline": run_pipeline,
            "load_activations": load_saved_activations,
        }
    )
