#!/usr/bin/env python3
"""
Production MASK Benchmark Functional Activations Creation Pipeline

DEPRECATED: This script has been converted to use the ITAS MASKPipeline class.
Please use the new demo_steered_inference.py script for steered inference.

This script is kept for backward compatibility but now uses the ITAS module internally.
"""

import os
import logging
import fire
import multiprocessing as mp
from typing import Optional
from datetime import datetime

# Fix CUDA multiprocessing issues
if __name__ == "__main__":
    mp.set_start_method("spawn", force=True)

# Set environment variables to avoid tokenizer warnings
os.environ["TOKENIZERS_PARALLELISM"] = "false"

# Import ITAS components
from itas.core.mask_pipeline import MASKPipeline, MASKPipelineConfig
from itas.core.steering import KnowledgeSelectionSteering

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(
            f'mask_steering_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'
        ),
    ],
)
logger = logging.getLogger(__name__)


def run_pipeline(
    model_name: str,
    sae_path: str,
    train_data_dir: str = "./data/mask_benchmark/csv_data/",
    target_layer: int = 16,
    steering_alpha: float = 1.5,
    top_k_proportion: float = 0.15,
    max_train_scenarios: Optional[int] = None,
    max_new_tokens: int = 1024,
    results_dir: str = "./results/",
    device: str = "auto",
    num_processes: Optional[int] = None,
):
    """
    Run the MASK benchmark truthfulness steering pipeline using ITAS MASKPipeline.

    This function provides backward compatibility with the original script interface
    but now uses the ITAS MASKPipeline class internally.
    """
    logger.warning(
        "⚠️  This function is deprecated. Please use demo_steered_inference.py for new projects."
    )
    logger.info("🔄 Using ITAS MASKPipeline for backward compatibility...")

    # Create pipeline configuration
    config = MASKPipelineConfig(
        model_name=model_name,
        sae_path=sae_path,
        train_data_dir=train_data_dir,
        target_layer=target_layer,
        steering_alpha=steering_alpha,
        top_k_proportion=top_k_proportion,
        max_train_scenarios=max_train_scenarios,
        max_new_tokens=max_new_tokens,
        results_dir=results_dir,
        device=device,
        num_processes=num_processes,
    )

    # Run pipeline
    pipeline = MASKPipeline(config)
    activations_path, config_path = pipeline.run_full_pipeline()

    logger.info("✅ Pipeline completed successfully!")
    logger.info(f"   Activations: {activations_path}")
    logger.info(f"   Config: {config_path}")
    logger.info("💡 Next steps: Use demo_steered_inference.py to run steered inference")

    return activations_path, config_path


def load_saved_activations(
    activations_path: str,
    config_path: str,
    device: str = "auto",
    num_processes: Optional[int] = None,
):
    """
    Load previously saved functional activations using ITAS MASKPipeline.

    This function provides backward compatibility with the original script interface.
    """
    logger.warning(
        "⚠️  This function is deprecated. Please use demo_steered_inference.py for new projects."
    )
    logger.info("🔄 Using ITAS MASKPipeline for backward compatibility...")

    # Load using MASKPipeline
    pipeline, steering, functional_activations = (
        MASKPipeline.load_from_saved_activations(activations_path, config_path)
    )

    logger.info("✅ Successfully loaded functional activations!")
    logger.info(f"   Scheming features: {len(functional_activations.scheming_indices)}")
    logger.info(f"   Truthful features: {len(functional_activations.truthful_indices)}")
    logger.info("💡 Next steps: Use demo_steered_inference.py to run steered inference")

    return steering, pipeline.mi_result, functional_activations


if __name__ == "__main__":
    fire.Fire(
        {
            "run_pipeline": run_pipeline,
            "load_activations": load_saved_activations,
        }
    )
