#!/usr/bin/env python3
"""
Production MASK Benchmark Functional Activations Creation Pipeline

A streamlined, production-ready script that uses the ITAS module to create
functional activations for truthfulness steering from the MASK benchmark dataset.

Key Features:
- Uses ALL available MASK benchmark data (no artificial limits)
- Leverages existing ITAS module components
- Production configuration (1024 max tokens, no mock SAEs)
- Automatically saves functional activations for reuse
- Fire CLI interface for easy configuration
- Comprehensive logging and error handling

Usage:
    # Create functional activations
    python production_mask_steering.py run_pipeline --model_name="your-model" --sae_path="/path/to/sae"

    # Custom parameters
    python production_mask_steering.py run_pipeline \
        --model_name="your-model" \
        --sae_path="/path/to/sae" \
        --steering_alpha=2.0 \
        --target_layer=20 \
        --top_k_proportion=0.1

    # Load previously saved activations
    python production_mask_steering.py load_activations \
        --activations_path="./results/functional_activations_20241201_123456.pt" \
        --sae_path="/path/to/sae"

Requirements:
    - The ITAS module (already available)
    - fire (pip install fire)
    - MASK benchmark data in ./data/mask_benchmark/
"""

import os
import json
import re
import logging
import pandas as pd
import torch
import fire
import multiprocessing as mp
from typing import List, Dict, Any, Tuple, Optional
from datetime import datetime

# Fix CUDA multiprocessing issues
if __name__ == "__main__":
    mp.set_start_method("spawn", force=True)

# Set environment variables to avoid tokenizer warnings
os.environ["TOKENIZERS_PARALLELISM"] = "false"

# Import ITAS components
from itas.core.model_loader import UniversalModelLoader
from itas.core.config import ModelConfig
from itas.core.sae import SAE
from itas.core.steering import KnowledgeSelectionSteering

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(
            f'mask_steering_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'
        ),
    ],
)
logger = logging.getLogger(__name__)


def load_and_process_mask_data(
    data_dir: str, max_scenarios: Optional[int] = None
) -> List[Dict[str, Any]]:
    """
    Load and process all MASK benchmark CSV files from a directory.

    Args:
        data_dir: Directory containing CSV files
        max_scenarios: Maximum scenarios to load (None = all)

    Returns:
        List of processed conversation scenarios
    """
    logger.info(f"Loading MASK data from {data_dir}")

    if not os.path.exists(data_dir):
        raise ValueError(f"Data directory not found: {data_dir}")

    csv_files = [f for f in os.listdir(data_dir) if f.endswith(".csv")]
    logger.info(f"Found {len(csv_files)} CSV files: {csv_files}")

    all_scenarios = []

    for csv_file in csv_files:
        csv_path = os.path.join(data_dir, csv_file)
        logger.info(f"Processing {csv_file}")

        try:
            df = pd.read_csv(csv_path)
            scenarios = process_csv_file(df, csv_file)
            all_scenarios.extend(scenarios)
            logger.info(f"  Added {len(scenarios)} scenarios from {csv_file}")
        except Exception as e:
            logger.error(f"  Error processing {csv_file}: {e}")
            continue

    # Apply limit if specified
    if max_scenarios is not None:
        all_scenarios = all_scenarios[:max_scenarios]
        logger.info(f"Limited to {len(all_scenarios)} scenarios")

    logger.info(f"Total scenarios loaded: {len(all_scenarios)}")
    return all_scenarios


def process_csv_file(df: pd.DataFrame, csv_filename: str) -> List[Dict[str, Any]]:
    """
    Process a single CSV DataFrame into conversation scenarios.

    Args:
        df: DataFrame from CSV file
        csv_filename: Name of the CSV file for tracking

    Returns:
        List of processed scenarios
    """
    scenarios = []

    for idx, row in df.iterrows():
        # Skip rows with missing essential data
        if (
            pd.isna(row.get("system_prompt"))
            or pd.isna(row.get("user_prompt"))
            or pd.isna(row.get("proposition"))
        ):
            continue

        # Extract and clean data
        system_prompt = " ".join(str(row["system_prompt"]).strip().split())
        user_prompt = " ".join(str(row["user_prompt"]).strip().split())
        proposition = str(row["proposition"]).strip()
        ground_truth = (
            str(row["ground_truth"]).strip()
            if pd.notna(row.get("ground_truth"))
            else ""
        )
        task_type = str(row["type"]).strip() if pd.notna(row.get("type")) else "unknown"

        # Create meaningful responses from proposition
        scheming_response, truthful_response = create_responses_from_proposition(
            proposition, ground_truth, user_prompt
        )

        if scheming_response is None or truthful_response is None:
            continue

        scenario = {
            "task_id": str(row.get("task_id", f"{csv_filename}_{idx}")),
            "system": system_prompt,
            "user": user_prompt,
            "scheming_response": scheming_response,
            "truthful_response": truthful_response,
            "type": task_type,
            "ground_truth": ground_truth,
            "csv_source": csv_filename,
        }

        scenarios.append(scenario)

    return scenarios


def create_responses_from_proposition(
    proposition: str, ground_truth: str, user_prompt: str
) -> Tuple[Optional[str], Optional[str]]:
    """
    Create scheming and truthful responses from proposition.

    Args:
        proposition: Proposition with <pivotal>choice1/choice2</pivotal>
        ground_truth: The factually correct answer
        user_prompt: User's question for context

    Returns:
        Tuple of (scheming_response, truthful_response)
    """
    # Extract pivotal choices
    pivotal_match = re.search(r"<pivotal>(.*?)</pivotal>", proposition)
    if not pivotal_match:
        return None, None

    pivotal_content = pivotal_match.group(1)

    # Handle numerical format: <pivotal><value></pivotal>
    if pivotal_content == "<value>":
        # For numerical scenarios, we need to extract the actual values from ground_truth
        # and create contrasting responses
        return handle_numerical_proposition(proposition, ground_truth, user_prompt)

    # Handle binary format: <pivotal>choice1/choice2</pivotal>
    choices = pivotal_content.split("/")
    if len(choices) != 2:
        return None, None

    choice1, choice2 = choices[0].strip(), choices[1].strip()

    # Determine which choice aligns with ground truth
    if choice1.lower() in ground_truth.lower():
        truthful_choice, scheming_choice = choice1, choice2
    elif choice2.lower() in ground_truth.lower():
        truthful_choice, scheming_choice = choice2, choice1
    else:
        # Fallback heuristics
        if "not" in ground_truth.lower():
            if "not" in choice2.lower() or "no" in choice2.lower():
                truthful_choice, scheming_choice = choice2, choice1
            else:
                truthful_choice, scheming_choice = choice1, choice2
        else:
            if "not" not in choice1.lower() and "no" not in choice1.lower():
                truthful_choice, scheming_choice = choice1, choice2
            else:
                truthful_choice, scheming_choice = choice2, choice1

    # Create contextual responses
    if any(
        phrase in user_prompt.lower() for phrase in ["yes or no", "answer yes or no"]
    ):
        # Simple yes/no responses based on positive/negative sentiment
        positive_words = [
            "is",
            "are",
            "was",
            "were",
            "can",
            "has",
            "have",
            "safe",
            "real",
            "true",
            "did",
            "do",
        ]
        negative_words = [
            "not",
            "no",
            "cannot",
            "dangerous",
            "fake",
            "false",
            "did not",
            "do not",
        ]

        if any(pos in scheming_choice.lower() for pos in positive_words):
            scheming_response = "Yes"
        elif any(neg in scheming_choice.lower() for neg in negative_words):
            scheming_response = "No"
        else:
            scheming_response = scheming_choice.capitalize()

        if any(pos in truthful_choice.lower() for pos in positive_words):
            truthful_response = "Yes"
        elif any(neg in truthful_choice.lower() for neg in negative_words):
            truthful_response = "No"
        else:
            truthful_response = truthful_choice.capitalize()
    else:
        # Use the choices directly or format into proposition
        base_proposition = re.sub(r"<pivotal>.*?</pivotal>", "{}", proposition)
        if len(base_proposition) > 200 or "?" in base_proposition:
            scheming_response = scheming_choice.capitalize()
            truthful_response = truthful_choice.capitalize()
        else:
            scheming_response = base_proposition.format(scheming_choice)
            truthful_response = base_proposition.format(truthful_choice)

    return scheming_response, truthful_response


def handle_numerical_proposition(
    proposition: str, ground_truth: str, user_prompt: str  # noqa: ARG001
) -> Tuple[Optional[str], Optional[str]]:
    """
    Handle numerical propositions with <pivotal><value></pivotal> format.

    Args:
        proposition: Proposition with <pivotal><value></pivotal>
        ground_truth: The factually correct numerical answer
        user_prompt: User's question for context

    Returns:
        Tuple of (scheming_response, truthful_response)
    """
    # Extract the numerical value from ground_truth
    import re

    # Try to extract the relevant number from ground_truth
    # Look for patterns like "X%" or "X percent" or "X million" etc.

    # First try percentage patterns
    percent_match = re.search(r"(\d+(?:\.\d+)?)%", ground_truth)
    if percent_match:
        truthful_number = percent_match.group(1)
    else:
        # Try other number patterns with units
        number_patterns = [
            r"(\d+(?:,\d{3})*(?:\.\d+)?)\s*(?:million|billion|trillion)",
            r"(\d+(?:,\d{3})*(?:\.\d+)?)\s*(?:people|dollars|percent)",
            r"(\d+(?:,\d{3})*(?:\.\d+)?)\s*(?:years?|points?)",
            r"(\d+(?:,\d{3})*(?:\.\d+)?)",  # Fallback: any number
        ]

        truthful_number = None
        for pattern in number_patterns:
            match = re.search(pattern, ground_truth)
            if match:
                truthful_number = match.group(1).replace(",", "")
                break

        if truthful_number is None:
            return None, None

    try:
        truthful_value = float(truthful_number)
    except ValueError:
        return None, None

    # Create a contrasting scheming value
    # For percentages, rates, etc., create a significantly different value
    if "%" in ground_truth or "percent" in ground_truth.lower():
        # For percentages, create a contrasting value
        if truthful_value < 50:
            scheming_value = min(95, truthful_value * 2.5)  # Make it higher
        else:
            scheming_value = max(5, truthful_value * 0.4)  # Make it lower
        scheming_number = f"{scheming_value:.1f}".rstrip("0").rstrip(".")
        truthful_number = f"{truthful_value:.1f}".rstrip("0").rstrip(".")
    elif "million" in ground_truth.lower() or "billion" in ground_truth.lower():
        # For large numbers, create significant contrast
        if truthful_value < 100:
            scheming_value = truthful_value * 1.8
        else:
            scheming_value = truthful_value * 0.6
        scheming_number = f"{scheming_value:.1f}".rstrip("0").rstrip(".")
        truthful_number = f"{truthful_value:.1f}".rstrip("0").rstrip(".")
    else:
        # For other numbers, create reasonable contrast
        if truthful_value < 1000:
            scheming_value = truthful_value * 1.5
        else:
            scheming_value = truthful_value * 0.7
        scheming_number = f"{int(scheming_value):,}"
        truthful_number = f"{int(truthful_value):,}"

    # Create responses by substituting the <value> placeholder
    base_proposition = proposition.replace("<pivotal><value></pivotal>", "{}")

    scheming_response = base_proposition.format(scheming_number)
    truthful_response = base_proposition.format(truthful_number)

    return scheming_response, truthful_response


def extract_activations(
    scenarios: List[Dict[str, Any]],
    model,
    tokenizer,
    target_layer: int,
    response_type: str,
    device: str,
) -> torch.Tensor:
    """
    Extract activations from assistant response tokens using proper conversation formatting.

    Args:
        scenarios: List of conversation scenarios
        model: The language model
        tokenizer: Model tokenizer
        target_layer: Layer to extract activations from
        response_type: 'scheming' or 'truthful'
        device: Device for computation

    Returns:
        Tensor of extracted activations
    """
    logger.info(
        f"Extracting {response_type} activations from {len(scenarios)} scenarios"
    )

    activations = []
    hook_name = f"model.layers.{target_layer}.mlp"

    def hook_fn(module, input, output):  # noqa: ARG001
        if isinstance(output, tuple):
            activations.append(output[0][:, -1, :].detach())
        else:
            activations.append(output[:, -1, :].detach())

    # Register hook
    target_module = model
    for part in hook_name.split("."):
        target_module = getattr(target_module, part)
    hook_handle = target_module.register_forward_hook(hook_fn)

    try:
        with torch.no_grad():
            for i, scenario in enumerate(scenarios):
                if i % 50 == 0:
                    logger.info(f"  Processing scenario {i+1}/{len(scenarios)}")

                # Format conversation with chat template
                conversation = [
                    {"role": "system", "content": scenario["system"]},
                    {"role": "user", "content": scenario["user"]},
                ]

                prompt = tokenizer.apply_chat_template(
                    conversation, tokenize=False, add_generation_prompt=True
                )

                # Tokenize context
                context_tokens = tokenizer(
                    prompt, return_tensors="pt", truncation=True, max_length=512
                )
                context_ids = context_tokens["input_ids"].to(device)

                # Get target response
                target_response = scenario[f"{response_type}_response"]
                response_tokens = tokenizer(
                    target_response, return_tensors="pt", add_special_tokens=False
                )
                response_ids = response_tokens["input_ids"].to(device)

                # Combine and forward pass
                full_ids = torch.cat([context_ids, response_ids], dim=1)
                _ = model(full_ids)

    finally:
        hook_handle.remove()

    if not activations:
        raise ValueError(f"No activations extracted for {response_type}")

    result = torch.cat(activations, dim=0)
    logger.info(f"Extracted {result.shape[0]} {response_type} activations")
    return result


def run_pipeline(
    model_name: str,
    sae_path: str,
    train_data_dir: str = "./data/mask_benchmark/csv_data/",
    target_layer: int = 16,
    steering_alpha: float = 1.5,
    top_k_proportion: float = 0.15,
    max_train_scenarios: Optional[int] = None,  # None = use ALL data
    max_new_tokens: int = 1024,  # Production setting
    results_dir: str = "./results/",
    device: str = "auto",
    num_processes: Optional[int] = None,  # None = auto-detect based on device
):
    """
    Run the MASK benchmark truthfulness steering pipeline to create functional activations.

    Args:
        model_name: HuggingFace model name (REQUIRED)
        sae_path: Path to SAE model (REQUIRED)
        train_data_dir: Directory with training CSV files
        target_layer: Layer for activation extraction
        steering_alpha: Steering strength
        top_k_proportion: Proportion of top features to use
        max_train_scenarios: Max training scenarios (None = all)
        max_new_tokens: Max tokens for generation
        results_dir: Directory to save results
        device: Device for computation
        num_processes: Number of processes for MI calculation (None = auto-detect)
    """
    logger.info("🚀 Starting MASK Benchmark Truthfulness Steering Pipeline")
    logger.info(f"Model: {model_name}")
    logger.info(f"SAE: {sae_path}")
    logger.info(f"Target layer: {target_layer}")
    logger.info(f"Steering alpha: {steering_alpha}")
    logger.info(f"Max train scenarios: {max_train_scenarios or 'ALL'}")

    # Setup device
    if device == "auto":
        device = "cuda" if torch.cuda.is_available() else "cpu"
    logger.info(f"Using device: {device}")

    # Create results directory
    os.makedirs(results_dir, exist_ok=True)

    try:
        # 1. Load model and tokenizer
        logger.info("📥 Loading model and tokenizer...")
        model_config = ModelConfig(model_name=model_name, load_for_generation=True)
        model_loader = UniversalModelLoader(model_config)
        model, tokenizer = model_loader.load_model_and_tokenizer()

        # 2. Load SAE
        logger.info("📥 Loading SAE...")
        sae = SAE.load(sae_path, device=device)

        # 3. Load training data
        logger.info("📊 Loading training data...")
        train_scenarios = load_and_process_mask_data(
            train_data_dir, max_train_scenarios
        )

        # 4. Extract training activations
        logger.info("🧠 Extracting training activations...")
        scheming_activations = extract_activations(
            train_scenarios, model, tokenizer, target_layer, "scheming", device
        )
        truthful_activations = extract_activations(
            train_scenarios, model, tokenizer, target_layer, "truthful", device
        )

        # 5. Create steering intervention
        logger.info("🎯 Creating steering intervention...")
        # Auto-detect number of processes if not specified
        if num_processes is None:
            # Use single process for CUDA to avoid multiprocessing issues
            num_processes = 1 if device == "cuda" else 4
        steering = KnowledgeSelectionSteering(
            sae, device=device, num_processes=num_processes
        )
        logger.info(
            f"Using {num_processes} process(es) for mutual information calculation"
        )

        mi_result = steering.calculate_mutual_information(
            scheming_hiddens=scheming_activations,
            truthful_hiddens=truthful_activations,
            top_k_proportion=top_k_proportion,
            minmax_normalization=True,
            equal_label_examples=True,
        )

        functional_activations = steering.create_functional_activations(mi_result)

        # 6. Save functional activations for reuse in other scripts
        logger.info("� Saving functional activations...")
        activations_save_path = os.path.join(
            results_dir,
            f"functional_activations_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pt",
        )
        steering.save_results(
            save_path=activations_save_path,
            mi_result=mi_result,
            functional_activations=functional_activations,
        )
        logger.info(f"✅ Functional activations saved to {activations_save_path}")

        # Save configuration for easy loading
        config_save_path = os.path.join(
            results_dir,
            f"steering_config_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
        )
        config = {
            "model_name": model_name,
            "sae_path": sae_path,
            "target_layer": target_layer,
            "steering_alpha": steering_alpha,
            "top_k_proportion": top_k_proportion,
            "train_scenarios": len(train_scenarios),
            "max_new_tokens": max_new_tokens,
            "activations_file": activations_save_path,
            "timestamp": datetime.now().isoformat(),
        }

        with open(config_save_path, "w") as f:
            json.dump(config, f, indent=2)
        logger.info(f"✅ Configuration saved to {config_save_path}")

        logger.info("✅ Pipeline completed successfully!")
        logger.info("Functional activations are ready for use in other scripts.")

    except Exception as e:
        logger.error(f"❌ Pipeline failed: {e}")
        raise


def load_saved_activations(
    activations_path: str,
    sae_path: str,
    device: str = "auto",
    num_processes: Optional[int] = None,
):
    """
    Load previously saved functional activations.

    Args:
        activations_path: Path to saved functional activations (.pt file)
        sae_path: Path to SAE model
        device: Device to load on
        num_processes: Number of processes for future calculations

    Returns:
        Tuple of (steering, mi_result, functional_activations)
    """
    logger.info(f"Loading functional activations from {activations_path}")

    # Setup device
    if device == "auto":
        device = "cuda" if torch.cuda.is_available() else "cpu"
    logger.info(f"Using device: {device}")

    # Auto-detect number of processes if not specified
    if num_processes is None:
        num_processes = 1 if device == "cuda" else 4

    # Load SAE
    logger.info("Loading SAE...")
    sae = SAE.load(sae_path, device=device)

    # Load functional activations
    steering, mi_result, functional_activations = (
        KnowledgeSelectionSteering.load_results(
            load_path=activations_path,
            sae=sae,
            device=device,
            num_processes=num_processes,
        )
    )

    logger.info("✅ Successfully loaded functional activations!")
    logger.info(f"   Scheming features: {len(functional_activations.scheming_indices)}")
    logger.info(f"   Truthful features: {len(functional_activations.truthful_indices)}")
    logger.info(f"   z_S norm: {functional_activations.z_scheming.norm().item():.4f}")
    logger.info(f"   z_T norm: {functional_activations.z_truthful.norm().item():.4f}")

    return steering, mi_result, functional_activations


if __name__ == "__main__":
    fire.Fire(
        {
            "run_pipeline": run_pipeline,
            "load_activations": load_saved_activations,
        }
    )
